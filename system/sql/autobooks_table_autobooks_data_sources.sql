-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 16, 2025 at 08:47 AM
-- Table: autobooks_data_sources
-- Records: 9

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_data_sources`
--

DROP TABLE IF EXISTS `autobooks_data_sources`;
CREATE TABLE `autobooks_data_sources` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the data source',
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional description of what this data source provides',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'other' COMMENT 'Data source category (data_table, email, users, system, autodesk, other)',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT 'Data source status (active, inactive, draft)',
  `table_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Primary database table name (for backward compatibility)',
  `tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of all tables used in this data source',
  `table_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping table names to their aliases (e.g., {"users": "u", "posts": "p"})',
  `joins` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of join configurations with type, tables, columns, and aliases',
  `selected_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of selected columns from all tables',
  `column_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping column keys to their custom aliases (e.g., {"users.first_name": "full_name"})',
  `custom_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of custom SQL columns with expressions and aliases',
  `filters` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of filter configurations for WHERE clauses',
  `sorting` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of sorting rules for ORDER BY clauses',
  `limits` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object with limit and offset settings for pagination',
  `column_mapping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON column mapping configuration for advanced use cases',
  `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'When the data source was created',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'When the data source was last updated',
  `grouping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of grouping rules (GROUP BY)',
  `custom_tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of custom SQL tables and subqueries with aliases',
  `data_source_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'standard' COMMENT 'Type of data source: standard or multi_table_merger',
  `table_patterns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of wildcard patterns for multi-table merger',
  `explicit_tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of explicitly selected tables for multi-table merger',
  `resolved_tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of resolved table names from patterns and explicit selection',
  `mapping_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Column mapping method for multi-table merger: like_for_like, manual, unified_field_mapper',
  `column_mappings` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of manual column mappings for multi-table merger',
  `unified_mappings` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object of unified field mapper mappings for multi-table merger',
  `reference_table` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Reference table name for column mapping assistance',
  `search_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of searchable column names for full-text search',
  PRIMARY KEY (`id`),
  KEY `idx_status_category` (`status`,`category`) COMMENT 'Index for filtering by status and category',
  KEY `idx_table_name` (`table_name`) COMMENT 'Index for backward compatibility queries',
  KEY `idx_created_by` (`created_by`) COMMENT 'Index for user-specific queries',
  KEY `idx_created_at` (`created_at`) COMMENT 'Index for chronological queries',
  KEY `idx_name` (`name`),
  KEY `idx_category_status` (`category`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns with full multi-table support, aliases, custom columns, sorting, and limits';

-- Dumping data for table `autobooks_data_sources`
--

INSERT INTO `autobooks_data_sources` (`id`, `name`, `description`, `category`, `status`, `table_name`, `tables`, `table_aliases`, `joins`, `selected_columns`, `column_aliases`, `custom_columns`, `filters`, `sorting`, `limits`, `column_mapping`, `created_by`, `created_at`, `updated_at`, `grouping`, `custom_tables`, `data_source_type`, `table_patterns`, `explicit_tables`, `resolved_tables`, `mapping_method`, `column_mappings`, `unified_mappings`, `reference_table`, `search_columns`) VALUES
('1', 'Autodesk_autorenew', '', 'other', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"}]', '{\"autodesk_subscriptions\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\",\"paymentMethod\",\"offeringId\",\"offeringCode\",\"offeringName\",\"autoRenew\"],\"autodesk_accounts\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\",\"email\",\"city\",\"postal_code\"]}', '[]', '[]', '[{\"column\":\"autoRenew\",\"operator\":\"=\",\"value\":\"ON\"},{\"column\":\"status\",\"operator\":\"=\",\"value\":\"Active\"}]', '[]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-07-25 15:52:00', '2025-07-28 10:56:21', '[{\"column\":\"autodesk_accounts.account_csn\"}]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('30', 'Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"],\"lastquote\":[\"quote_id\",\"quote_number\",\"quoted_date\"]}', '[]', '[{\"sql\":\"CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END\",\"alias\":\"subs_enddatediff\"}]', '[]', '[{\"column\":\"subs_enddatediff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', NULL, '1', '2025-07-31 19:32:34', '2025-08-13 11:15:51', '[]', '[{\"alias\":\"lastquote\",\"sql\":\"SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\\n\\nFROM autodesk_quote_line_items qi\\nJOIN autodesk_quotes q ON q.id = qi.quote_id\\n\\nWHERE qi.subscription_id IS NOT NULL\\n  AND q.quote_status NOT IN (\'Expired\', \'Cancelled\')\\n\\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\\n\\nLIMIT 1\",\"join_type\":\"LEFT JOIN\",\"join_condition\":\"subs.subscriptionId = lastquote.subscription_id\",\"columns\":\"quote_id, quote_status, quote_number, quoted_date\",\"description\":\"\"}]', 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[\"autodesk_subscriptions.subscriptionId\",\"autodesk_subscriptions.subscriptionReferenceNumber\",\"autodesk_subscriptions.status\",\"autodesk_subscriptions.accessModel\",\"autodesk_subscriptions.billingBehavior\",\"autodesk_subscriptions.billingFrequency\",\"autodesk_subscriptions.connectivity\",\"autodesk_subscriptions.connectivityInterval\",\"autodesk_subscriptions.intendedUsage\",\"autodesk_subscriptions.servicePlan\",\"autodesk_subscriptions.term\",\"autodesk_subscriptions.paymentMethod\",\"autodesk_subscriptions.offeringCode\",\"autodesk_subscriptions.offeringName\",\"autodesk_subscriptions.recordType\",\"autodesk_subscriptions.autoRenew\",\"autodesk_subscriptions.opportunityNumber\",\"autodesk_subscriptions.soldTo_id\",\"autodesk_subscriptions.solutionProvider_id\",\"autodesk_subscriptions.nurtureReseller_id\",\"autodesk_subscriptions.endCustomer_id\"]'),
('31', 'Autodesk Accounts', 'Customer account information', 'autodesk', 'active', 'autodesk_accounts', '[\"autodesk_accounts\"]', NULL, '[]', '{\"autodesk_accounts\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\",\"email\",\"phone\",\"city\",\"postal_code\",\"country\",\"account_type\"]}', '{\"id\":\"account_id\",\"name\":\"company_name\",\"email\":\"email\",\"phone\":\"phone\",\"country\":\"country\",\"account_type\":\"account_type\"}', NULL, '[{\"column\":\"name\",\"operator\":\"LIKE\",\"value\":\"Ltd\"},{\"column\":\"country\",\"operator\":\"=\",\"value\":\"United Kingdom\"},{\"column\":\"account_type\",\"operator\":\"=\",\"value\":\"Customer\"}]', NULL, NULL, NULL, '1', '2025-07-31 19:32:34', '2025-07-31 20:32:34', NULL, NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('32', 'Expiring Subscriptions', 'Active subscriptions expiring within 90 days', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', NULL, '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_id\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.id\",\"left_alias\":\"\",\"right_alias\":\"soldto\"}]', '{\"autodesk_subscriptions\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\",\"offeringCode\",\"offeringName\",\"autoRenew\",\"soldTo_id\"],\"autodesk_accounts\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\",\"email\",\"city\",\"postal_code\"]}', '{\"subscriptionId\":\"subscription_id\",\"offeringName\":\"product_name\",\"endDate\":\"expiry_date\",\"quantity\":\"quantity\",\"soldTo_id\":\"customer_id\"}', NULL, '[{\"column\":\"status\",\"operator\":\"=\",\"value\":\"Active\"},{\"column\":\"endDate\",\"operator\":\">\",\"value\":\"2024-01-01\"},{\"column\":\"endDate\",\"operator\":\"<\",\"value\":\"2024-12-31\"}]', NULL, NULL, NULL, '1', '2025-07-31 19:32:34', '2025-07-31 20:32:34', NULL, NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('33', 'Autodesk Email History', 'Email communication history', 'autodesk', 'active', 'autodesk_email_history', '[\"autodesk_email_history\",\"autodesk_subscriptions\"]', '[]', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_email_history\",\"left_column\":\"autodesk_email_history.subscription_id\",\"right_table\":\"autodesk_subscriptions\",\"right_column\":\"autodesk_subscriptions.id\",\"left_alias\":\"\",\"right_alias\":\"sub\"}]', '{\"autodesk_email_history\":[\"id\",\"subscription_id\"]}', '[]', '[]', '[]', '[]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', NULL, '1', '2025-07-31 19:32:34', '2025-08-01 15:42:32', '[]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('34', 'Copy of Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"soldto\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.solutionProvider_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"solpro\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.nurtureReseller_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"resell\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"]}', '[]', '[{\"sql\":\"DATEDIFF(subs.enddate, NOW())\",\"alias\":\"enddate_diff\"}]', '[]', '[{\"column\":\"enddate_diff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-02 09:30:09', '2025-08-02 09:30:09', '[]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('35', 'Full Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"soldto\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.solutionProvider_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"solpro\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.nurtureReseller_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"resell\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"]}', '[]', '[{\"sql\":\"DATEDIFF(subs.enddate, NOW())\",\"alias\":\"enddate_diff\"}]', '[]', '[{\"column\":\"enddate_diff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-02 09:31:53', '2025-08-02 09:35:28', '[]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('51', 'tstAutodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"soldto\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.solutionProvider_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"solpro\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.nurtureReseller_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"resell\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"]}', '[]', '[{\"sql\":\"DATEDIFF(subs.enddate, NOW())\",\"alias\":\"enddate_diff\"}]', '[]', '[{\"column\":\"enddate_diff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-15 13:47:58', '2025-08-15 13:49:31', '[]', '[]', 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('53', 'CSV Import: Import_bluebeam', 'Auto-generated data source for CSV import (489 rows, 12 columns)', 'csv_import', 'active', 'autobooks_import_bluebeam_data', '[\"autobooks_import_bluebeam_data\"]', NULL, NULL, '{\"autobooks_import_bluebeam_data\":[\"serial_number\",\"name\",\"contract\",\"product_name\",\"quantity\",\"end_date\",\"account_primary_reseller_name\",\"order_po_number\",\"order_shipping_address\",\"order_shipping_city\",\"order_shipping_state_province\",\"order_shipping_country\"]}', '{\"autobooks_import_bluebeam_data.serial_number\":\"subscription_reference\",\"autobooks_import_bluebeam_data.name\":\"company_name\",\"autobooks_import_bluebeam_data.product_name\":\"company_name\",\"autobooks_import_bluebeam_data.account_primary_reseller_name\":\"company_name\"}', NULL, NULL, NULL, NULL, NULL, '2', '2025-08-16 08:46:00', '2025-08-16 08:46:00', NULL, NULL, 'standard', NULL, NULL, '[\"autobooks_import_bluebeam_data\"]', 'unified_field_mapper', NULL, '{\"min_confidence\":75,\"applied\":{\"serial_number\":{\"category\":\"subscription_reference\",\"field_name\":\"subscription_reference\",\"confidence\":100,\"final_score\":94,\"normalized_fields\":[\"subscription_reference\",\"subs_subscriptionReferenceNumber\",\"subscription_id\"]},\"name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":100,\"final_score\":67.4,\"normalized_fields\":[\"company_name\",\"endcust_name\",\"end_customer_name\"]},\"product_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":85,\"final_score\":61.4,\"normalized_fields\":[\"company_name\",\"endcust_name\",\"end_customer_name\"]},\"account_primary_reseller_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":85,\"final_score\":61.4,\"normalized_fields\":[\"company_name\",\"endcust_name\",\"end_customer_name\"]}},\"overrides\":[]}', NULL, NULL);

COMMIT;
