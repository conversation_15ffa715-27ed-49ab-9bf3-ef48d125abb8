[legacy_database_errors] [2025-08-10 13:25:25] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-10 13:25:25\n)\n
[legacy_database_errors] [2025-08-11 07:48:25] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-11 07:48:25\n)\n
[legacy_database_errors] [2025-08-11 13:53:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-11 13:53:44\n)\n
[legacy_database_errors] [2025-08-11 13:53:47] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-11 13:53:47\n)\n
[legacy_database_errors] [2025-08-11 14:27:02] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0\n    [ip_address] => ************\n    [timestamp] => 2025-08-11 14:27:02\n)\n
