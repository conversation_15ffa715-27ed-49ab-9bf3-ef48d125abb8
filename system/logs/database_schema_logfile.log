[database_schema] [2025-08-13 09:27:41] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VA<PERSON><PERSON><PERSON>(255) NULL, `name` VA<PERSON><PERSON><PERSON>(255) NULL, `contract` VA<PERSON>HAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-13 09:27:41\n)\n
