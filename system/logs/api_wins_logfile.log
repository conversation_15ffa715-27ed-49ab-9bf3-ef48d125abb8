[api_wins] [2025-08-13 08:47:03] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 08:47:03] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 08:47:09] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 08:54:25] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:01:03] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:01:04] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:03:14] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:03:14] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:03:19] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:05:15] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:05:16] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:11] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:11] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:16] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:17] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:17] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(37) "api\data_sources\update_query_preview"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:06:34] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:09:04] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:09:04] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:09:08] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:09:09] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:10:08] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\delete_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:17] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:17] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:20] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:21] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:26] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(23) "api\data_sources\delete"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:28] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(26) "api\nav_tree\add_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:17:32] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(32) "api\nav_tree\get_template_fields"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:23] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:23] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:26] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(26) "api\nav_tree\add_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:29] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(32) "api\nav_tree\get_template_fields"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:37] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:37] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:38] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:38] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:38] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(29) "api\nav_tree\update_route_key"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:40] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(27) "api\nav_tree\save_nav_entry"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:41] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(36) "api\nav_tree\save_nav_entry_progress"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:42] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(13) "api\nav_tree\"\n  ["function_call"]: string(36) "api\nav_tree\save_nav_entry_progress"\n  ["path_parts"]: array(1) {\n    [0]: string(8) "nav_tree"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:48] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:48] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:50] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:50] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:53] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:55] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:27:56] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:00] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:04] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:23] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:23] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:27] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(26) "api\data_sources\edit_view"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:28] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(39) "api\data_sources\query_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:28:28] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(17) "api\data_sources\"\n  ["function_call"]: string(38) "api\data_sources\data_preview_fragment"\n  ["path_parts"]: array(1) {\n    [0]: string(12) "data_sources"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:30:29] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:30:30] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:30:33] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:06] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:06] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:11] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:11] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:51] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:51] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:31:55] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:35:52] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:35:52] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:35:56] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:36:33] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:44:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:44:37] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:52:33] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:52:34] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:55:44] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:55:44] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:55:47] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 09:59:58] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:01:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(34) "api\notifications\get_unread_count"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:01:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(18) "api\notifications\"\n  ["function_call"]: string(35) "api\notifications\get_notifications"\n  ["path_parts"]: array(1) {\n    [0]: string(13) "notifications"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:01:39] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:01:41] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:01:43] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:02:00] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:02:02] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:02:04] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:02:05] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:02:15] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(25) "api\data_table\pagination"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:24] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:27] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:31] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:35] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:36] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:37] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:39] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
[api_wins] [2025-08-13 10:03:40] [layout-api.edge.php:56]  \n<!--\n********************************************************************************************************************************************************\ncalling: layout-api.edge.php > include() 56\narray(3) {\n  ["namespace"]: string(15) "api\data_table\"\n  ["function_call"]: string(32) "api\data_table\data_table_filter"\n  ["path_parts"]: array(1) {\n    [0]: string(10) "data_table"\n  }\n}\n\n    ---------------------------------------------------------------------------- \n      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 143\n         <strong>Arguments:</strong> \n         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 136\n         <strong>Arguments:</strong> \n         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"\n      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 211\n         <strong>Arguments:</strong> \n         0: "layout-api"\n         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...\n\n----------------------------------------------------------------------------\n-->\n
