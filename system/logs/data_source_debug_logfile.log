[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1564] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1565] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1566] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1567] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1761] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1762] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1763] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-13 09:06:17] [data_sources.api.php:1764] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1564] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1565] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1566] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1567] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1761] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1762] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1763] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-13 09:06:31] [data_sources.api.php:1764] query_preview_fragment - final filters: []
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1564] query_preview_fragment - selected_tables param: [
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1565] query_preview_fragment - tables param: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1566] query_preview_fragment - selected_columns_json param: [
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1567] query_preview_fragment - selected_columns param: ["autobooks_import_bluebeam_data.id","autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name"]
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1761] query_preview_fragment - final tables: ["autobooks_import_bluebeam_data"]
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1762] query_preview_fragment - final joins: []
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1763] query_preview_fragment - final selected_columns: {"autobooks_import_bluebeam_data":["id","serial_number","name"]}
[data_source_debug] [2025-08-13 09:28:28] [data_sources.api.php:1764] query_preview_fragment - final filters: []
