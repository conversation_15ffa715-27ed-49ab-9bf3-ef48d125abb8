[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:42]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:43]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:51]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:53]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:57]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  Found 8 total data sources
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 40: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:65]  Found matching data source ID 40 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:134] Using existing data source ID: 40 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 08:47:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:177] Retrieved 50 rows from data source ID: 40
[data_source_template] [2025-08-13 09:03:12] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:42]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:03:12] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:43]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:03:12] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:51]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:03:12] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:53]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:57]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  Found 8 total data sources
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 40: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:65]  Found matching data source ID 40 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:134] Using existing data source ID: 40 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:03:13] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:177] Retrieved 50 rows from data source ID: 40
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:42]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:43]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:51]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:53]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:57]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  Found 8 total data sources
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 40: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:65]  Found matching data source ID 40 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:134] Using existing data source ID: 40 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:09:03] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:177] Retrieved 50 rows from data source ID: 40
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 7 total data sources
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:99]  Detected searchable columns for autobooks_import_bluebeam_data: []
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:132] Auto-created data source ID: 41 for table: autobooks_import_bluebeam_data with search columns
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:27:48] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:27:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:30:29] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:31:10] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:31:50] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:35:52] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 09:55:42] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:47]  === UPDATED TEMPLATE VERSION 2.0 STARTING ===
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:48]  Processing table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:56]  Table exists: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:58]  === NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:62]  Looking for data sources for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:63]  Found 8 total data sources
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 31: 'Autodesk Accounts' with table_name: 'autodesk_accounts'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 33: 'Autodesk Email History' with table_name: 'autodesk_email_history'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 30: 'Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 1: 'Autodesk_autorenew' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 34: 'Copy of Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 41: 'Data-Table with Data Source - autobooks_import_bluebeam_data' with table_name: 'autobooks_import_bluebeam_data'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:70]  Found matching data source ID 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 32: 'Expiring Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:67]  Checking data source ID 35: 'Full Autodesk Subscriptions' with table_name: 'autodesk_subscriptions'
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:139] Using existing data source ID: 41 for table: autobooks_import_bluebeam_data
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:241] Retrieved 50 rows from data source ID: 41
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:242] Columns for data table: [{"label":"Serial number","field":"serial_number","filter":true},{"label":"Name","field":"name","filter":true},{"label":"Contract","field":"contract","filter":true},{"label":"Product name","field":"product_name","filter":true},{"label":"Quantity","field":"quantity","filter":true},{"label":"End date","field":"end_date","filter":true},{"label":"Account primary reseller name","field":"account_primary_reseller_name","filter":true},{"label":"Order po number","field":"order_po_number","filter":true},{"label":"Order shipping address","field":"order_shipping_address","filter":true},{"label":"Order shipping city","field":"order_shipping_city","filter":true},{"label":"Order shipping state province","field":"order_shipping_state_province","filter":true},{"label":"Order shipping country","field":"order_shipping_country","filter":true},{"label":"Created at","field":"created_at","filter":true},{"label":"Updated at","field":"updated_at","filter":true}]
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:243] Fallback columns: [{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}]
[data_source_template] [2025-08-13 10:01:35] [bluebeam_ef71573ff4a639dcf559058fa7ba15ec.comp.php:398] Final safety check - columns count: 14, fallback count: 15
