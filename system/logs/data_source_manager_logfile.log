[data_source_manager] [2025-08-13 08:47:03] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:03:13] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:09:03] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:27:48] [data_source_manager.class.php:236] Created data source: Data-Table with Data Source - autobooks_import_bluebeam_data with ID: 41
[data_source_manager] [2025-08-13 09:27:48] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:27:50] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:30:29] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:31:10] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:31:50] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:35:52] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:55:42] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 09:55:47] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 09:55:47] [data_source_manager.class.php:632] Final search columns for autobooks_import_bluebeam_data: ["autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.title","autobooks_import_bluebeam_data.description"]
[data_source_manager] [2025-08-13 09:55:47] [data_source_manager.class.php:400] Search term: 'bsba', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.title","autobooks_import_bluebeam_data.description"]
[data_source_manager] [2025-08-13 09:55:47] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`title` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`description` LIKE '%bsba%')
[data_source_manager] [2025-08-13 10:01:35] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-13 10:01:39] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:01:39] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:01:39] [data_source_manager.class.php:400] Search term: 'baba', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:01:39] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%baba%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%baba%')
[data_source_manager] [2025-08-13 10:01:41] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:01:41] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:01:41] [data_source_manager.class.php:400] Search term: 'bs', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:01:41] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%bs%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%bs%')
[data_source_manager] [2025-08-13 10:01:43] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:01:43] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:01:43] [data_source_manager.class.php:400] Search term: 'bsba', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:01:43] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%bsba%')
[data_source_manager] [2025-08-13 10:02:00] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data`
[data_source_manager] [2025-08-13 10:02:02] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` ORDER BY `contract` ASC
[data_source_manager] [2025-08-13 10:02:04] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` ORDER BY `contract` ASC
[data_source_manager] [2025-08-13 10:02:05] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` ORDER BY `name` ASC
[data_source_manager] [2025-08-13 10:03:24] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:24] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:24] [data_source_manager.class.php:400] Search term: 'te', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:24] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%te%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%te%')
[data_source_manager] [2025-08-13 10:03:27] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:27] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:27] [data_source_manager.class.php:400] Search term: 'tele', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:27] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%tele%')
[data_source_manager] [2025-08-13 10:03:31] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:31] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:31] [data_source_manager.class.php:400] Search term: 'telephone', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:31] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%telephone%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%telephone%')
[data_source_manager] [2025-08-13 10:03:35] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:35] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:35] [data_source_manager.class.php:400] Search term: 'telephon', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:35] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%telephon%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%telephon%')
[data_source_manager] [2025-08-13 10:03:36] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:36] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:36] [data_source_manager.class.php:400] Search term: 'telepho', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:36] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%telepho%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%telepho%')
[data_source_manager] [2025-08-13 10:03:37] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:37] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:37] [data_source_manager.class.php:400] Search term: 'teleph', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:37] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%teleph%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%teleph%')
[data_source_manager] [2025-08-13 10:03:39] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:39] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:39] [data_source_manager.class.php:400] Search term: 'telep', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:39] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%telep%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%telep%')
[data_source_manager] [2025-08-13 10:03:40] [data_source_manager.class.php:586] Getting searchable columns for data source 41, table: autobooks_import_bluebeam_data
[data_source_manager] [2025-08-13 10:03:40] [data_source_manager.class.php:596] Using configured search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:40] [data_source_manager.class.php:400] Search term: 'tele', Data source ID: 41, Search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_source_manager] [2025-08-13 10:03:40] [data_source_manager.class.php:495] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`serial_number` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`name` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`contract` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`product_name` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`quantity` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`end_date` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`account_primary_reseller_name` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_po_number` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_address` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_city` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_state_province` LIKE '%tele%' OR `autobooks_import_bluebeam_data`.`order_shipping_country` LIKE '%tele%')
