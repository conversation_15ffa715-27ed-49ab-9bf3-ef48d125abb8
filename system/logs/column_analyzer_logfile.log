[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: serial_number, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: serial_number
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: serial_number
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for serial_number: {"original_name":"serial_number","suggested_name":"serial_number","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:482] No intelligent naming applied for serial_number (confidence: 0%, suggested: serial_number)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:493] Using standard formatting for serial_number
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for name: {"original_name":"name","suggested_name":"company_name","confidence":32.5,"reasoning":"Medium confidence match for 'company_name' (score: 32.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"requires_analysis":true,"confidence":30},"data_analysis":{"type":"company_name","confidence":51,"pattern_matches":{"business_entity":{"score":54,"matches":12,"total":20,"percentage":0.6},"business_keywords":{"score":51,"matches":12,"total":20,"percentage":0.6,"type":"company_name"},"software_keywords":{"score":8,"matches":2,"total":20,"percentage":0.1,"type":"product_name"},"industry_keywords":{"score":6,"matches":2,"total":20,"percentage":0.1,"type":"industry_sector"},"uk_locations":{"score":2.5,"matches":1,"total":20,"percentage":0.05,"type":"location"}},"sample_size":20},"context_analysis":{"type":"company_name","confidence":35,"reasoning":["Detected as product_sales_table table"]},"final_scores":{"company_name":32.5}}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:478] Intelligent naming applied: name -> company_name (confidence: 32.5)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: contract, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: contract
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: contract
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for contract: {"original_name":"contract","suggested_name":"numeric_id","confidence":30,"reasoning":"Medium confidence match for 'numeric_id' (score: 30)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"numeric_id","confidence":60,"pattern_matches":{"numeric_id":{"score":60,"matches":20,"total":20,"percentage":1}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"numeric_id":30}}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:478] Intelligent naming applied: contract -> numeric_id (confidence: 30)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: product_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: product_name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: product_name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for product_name: {"original_name":"product_name","suggested_name":"autobooks_product_name","confidence":28.5,"reasoning":"Low confidence - added context prefix (score: 28.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"product_name","confidence":95},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"product_name":28.5}}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:482] No intelligent naming applied for product_name (confidence: 28.5%, suggested: autobooks_product_name)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:493] Using standard formatting for product_name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: quantity, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: quantity
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: quantity
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for quantity: {"original_name":"quantity","suggested_name":"quantity","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:482] No intelligent naming applied for quantity (confidence: 0%, suggested: quantity)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:493] Using standard formatting for quantity
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: end_date, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: end_date
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: end_date
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for end_date: {"original_name":"end_date","suggested_name":"end_date","confidence":0,"reasoning":"No analysis results available","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"unknown","confidence":0,"reason":"No patterns matched"},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":[]}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:482] No intelligent naming applied for end_date (confidence: 0%, suggested: end_date)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:493] Using standard formatting for end_date
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: account_primary_reseller_name, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: account_primary_reseller_name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: account_primary_reseller_name
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for account_primary_reseller_name: {"original_name":"account_primary_reseller_name","suggested_name":"company_name","confidence":42.5,"reasoning":"Medium confidence match for 'company_name' (score: 42.5)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"company_name","confidence":85,"pattern_matches":{"business_entity":{"score":90,"matches":20,"total":20,"percentage":1},"business_keywords":{"score":85,"matches":20,"total":20,"percentage":1,"type":"company_name"},"software_keywords":{"score":80,"matches":20,"total":20,"percentage":1,"type":"product_name"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"company_name":42.5}}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:478] Intelligent naming applied: account_primary_reseller_name -> company_name (confidence: 42.5)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:396] Generator debug - column: order_shipping_address, predefined_label: null, use_intelligent: 1, table: autobooks_import_bluebeam_data
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:400] Generator debug - calling intelligent naming for column: order_shipping_address
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:461] get_intelligent_column_label called for: order_shipping_address
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:465] column_analyzer class exists, calling analyze_column
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:468] Analysis result for order_shipping_address: {"original_name":"order_shipping_address","suggested_name":"autobooks_order_shipping_address","confidence":25,"reasoning":"Low confidence - added context prefix (score: 25)","analysis_performed":true,"detailed_analysis":{"name_analysis":{"type":"unknown","confidence":0,"requires_analysis":true},"data_analysis":{"type":"location","confidence":50,"pattern_matches":{"uk_locations":{"score":50,"matches":20,"total":20,"percentage":1,"type":"location"}},"sample_size":20},"context_analysis":{"type":null,"confidence":0,"reasoning":[]},"final_scores":{"location":25}}}
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:482] No intelligent naming applied for order_shipping_address (confidence: 25%, suggested: autobooks_order_shipping_address)
[column_analyzer] [2025-08-13 09:27:42] [data_table_generator.class.php:493] Using standard formatting for order_shipping_address
