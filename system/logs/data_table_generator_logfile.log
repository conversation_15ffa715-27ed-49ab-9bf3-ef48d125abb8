[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-13 09:27:42] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
