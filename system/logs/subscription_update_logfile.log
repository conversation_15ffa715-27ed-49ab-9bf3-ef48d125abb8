[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 00:07:35
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5c6fa27f-b9d9-4949-a961-fe59468588f0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74700029815254\n            [status] => Expired\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-10T23:52:30.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T00:07:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 00:07:35
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5c6fa27f-b9d9-4949-a961-fe59468588f0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74700029815254\n            [status] => Expired\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-08-10T23:52:30.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T00:07:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74700029815254', status = 'Expired' ON DUPLICATE KEY UPDATE subscriptionId = '74700029815254', status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-11 00:07:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74700029815254', status = 'Expired' ON DUPLICATE KEY UPDATE subscriptionId = '74700029815254', status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 10:44:10
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 85c2508c-fa32-484b-819c-bf166072b6b9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72371865859320\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-14\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-11T09:23:17.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T10:44:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72371865859320', status = 'Active', quantity = 1, endDate = '2026-08-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72371865859320', status = 'Active', quantity = 1, endDate = '2026-08-14', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 10:44:10
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 85c2508c-fa32-484b-819c-bf166072b6b9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72371865859320\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-14\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-11T09:23:17.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T10:44:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 10:44:10] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72371865859320', status = 'Active', quantity = 1, endDate = '2026-08-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72371865859320', status = 'Active', quantity = 1, endDate = '2026-08-14', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 12:57:15
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f014dcac-8b29-49dc-880e-5864e86eb00c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72371865859320\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T11:20:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T12:57:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72371865859320', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72371865859320', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 12:57:15
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f014dcac-8b29-49dc-880e-5864e86eb00c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72371865859320\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T11:20:48.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T12:57:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 12:57:15] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72371865859320', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72371865859320', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 18:34:35
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1f606064-1a15-42e0-9fa7-14d356641c52\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69226925631631\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-16\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-11T17:28:58.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T18:34:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 18:34:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69226925631631', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69226925631631', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 18:34:36
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1f606064-1a15-42e0-9fa7-14d356641c52\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69226925631631\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-16\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-11T17:28:58.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T18:34:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 18:34:36] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69226925631631', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69226925631631', status = 'Active', quantity = 1, endDate = '2026-08-16', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 19:51:41
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8d16e00c-35f6-4321-aa13-3c56b7b97511\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75492703551740\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T19:14:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T19:51:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 19:51:41
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8d16e00c-35f6-4321-aa13-3c56b7b97511\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75492703551740\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T19:14:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T19:51:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 19:51:41] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-11 19:51:45] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75492703551740\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-11\n            [endDate] => 2025-09-30\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                            [type] => End Customer\n                            [address1] => Thomas Tredgold House 231 London Road\n                            [address2] => \n                            [address3] => \n                            [city] => Bishop'S Stortford\n                            [stateProvince] => HERTFORDSHIRE\n                            [postalCode] => CM23 3LA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Alexander\n                            [primaryAdminLastName] => Kannemeyer\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8603794\n                            [teamName] => Alexander Kannemeyer - 3794\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Alexander\n                            [last] => Kannemeyer\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-11 19:51:45] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-11 19:51:46] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75492703551740\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-11\n            [endDate] => 2025-09-30\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                            [type] => End Customer\n                            [address1] => Thomas Tredgold House 231 London Road\n                            [address2] => \n                            [address3] => \n                            [city] => Bishop'S Stortford\n                            [stateProvince] => HERTFORDSHIRE\n                            [postalCode] => CM23 3LA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Alexander\n                            [primaryAdminLastName] => Kannemeyer\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8603794\n                            [teamName] => Alexander Kannemeyer - 3794\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Alexander\n                            [last] => Kannemeyer\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-11 19:51:46] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 20:12:04
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 57d01d3b-80e3-4929-bee0-b9db299a2450\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69226925631631\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T19:11:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T20:12:02.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69226925631631', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69226925631631', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 20:12:04
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 57d01d3b-80e3-4929-bee0-b9db299a2450\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69226925631631\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T19:11:29.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T20:12:02.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-11 20:12:04] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69226925631631', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69226925631631', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 20:16:30
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 86526cb0-7150-4e4d-a636-3ca86348be83\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75492745675479\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T19:14:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T20:16:28.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-11 20:16:30
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 86526cb0-7150-4e4d-a636-3ca86348be83\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75492745675479\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-08-11T19:14:02.000+0000\n        )\n\n    [publishedAt] => 2025-08-11T20:16:28.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-11 20:16:30] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-08-11 20:16:33] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75492745675479\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-11\n            [endDate] => 2025-09-30\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                            [type] => End Customer\n                            [address1] => Thomas Tredgold House 231 London Road\n                            [address2] => \n                            [address3] => \n                            [city] => Bishop'S Stortford\n                            [stateProvince] => HERTFORDSHIRE\n                            [postalCode] => CM23 3LA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Alexander\n                            [primaryAdminLastName] => Kannemeyer\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8603794\n                            [teamName] => Alexander Kannemeyer - 3794\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Alexander\n                            [last] => Kannemeyer\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-11 20:16:33] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-08-11 20:16:34] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75492745675479\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-08-11\n            [endDate] => 2025-09-30\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000052\n            [offeringCode] => AECCOL\n            [offeringName] => AEC Collection\n            [marketingName] => Architecture Engineering & Construction Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => STORTFORD HOLDINGS Ltd\n                            [type] => End Customer\n                            [address1] => Thomas Tredgold House 231 London Road\n                            [address2] => \n                            [address3] => \n                            [city] => Bishop'S Stortford\n                            [stateProvince] => HERTFORDSHIRE\n                            [postalCode] => CM23 3LA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Alexander\n                            [primaryAdminLastName] => Kannemeyer\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8603794\n                            [teamName] => Alexander Kannemeyer - 3794\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Alexander\n                            [last] => Kannemeyer\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-08-11 20:16:34] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-12 08:15:48
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => dfe091f9-3975-41d6-989a-0aa1c0ce458d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69201884897330\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-13\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-12T08:00:43.000+0000\n        )\n\n    [publishedAt] => 2025-08-12T08:15:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-12 08:15:48
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => dfe091f9-3975-41d6-989a-0aa1c0ce458d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69201884897330\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-13\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-08-12T08:00:43.000+0000\n        )\n\n    [publishedAt] => 2025-08-12T08:15:46.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69201884897330', status = 'Active', quantity = 1, endDate = '2026-08-13', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69201884897330', status = 'Active', quantity = 1, endDate = '2026-08-13', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-12 08:15:48] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69201884897330', status = 'Active', quantity = 1, endDate = '2026-08-13', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69201884897330', status = 'Active', quantity = 1, endDate = '2026-08-13', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
