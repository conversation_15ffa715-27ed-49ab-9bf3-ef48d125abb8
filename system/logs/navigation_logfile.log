[navigation] [2025-08-13 09:10:08] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-13 09:27:40] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755077260.csv
[navigation] [2025-08-13 09:27:41] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755077260.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-13 09:27:42] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-13 09:27:42] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
