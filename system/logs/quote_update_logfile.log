[quote_update] [2025-08-13 08:55:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 08:55:15
[quote_update] [2025-08-13 08:55:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 08:55:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2ba03182-d9ee-493d-9af7-f44d6764ea1b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001027\n            [transactionId] => ac02bb63-e033-5b29-a6e6-da75b5181708\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1001027 status changed to Draft.\n            [modifiedAt] => 2025-08-13T08:55:12.901Z\n        )\n\n    [publishedAt] => 2025-08-13T08:55:13.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 08:55:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 08:55:15
[quote_update] [2025-08-13 08:55:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 08:55:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2ba03182-d9ee-493d-9af7-f44d6764ea1b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001027\n            [transactionId] => ac02bb63-e033-5b29-a6e6-da75b5181708\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1001027 status changed to Draft.\n            [modifiedAt] => 2025-08-13T08:55:12.901Z\n        )\n\n    [publishedAt] => 2025-08-13T08:55:13.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 08:55:17] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Wed, 13 Aug 2025 08:55:17 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3619\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 515859bf-00cd-4efa-948f-f704bddf7667\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PPHmVHJBoAMElgQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689c52f4-343b30cb2d421bc967fac06f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1001027\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-13T09:55:10+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1640\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 1640\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1640\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AIR HANDLING SYSTEMS\n                            [addressLine1] => Unit 3-5 Furnace Industrial Estate\n                            [city] => Shildon\n                            [stateProvinceCode] => \n                            [stateProvince] => COUNTY DURHAM\n                            [postalCode] => DL4 1QB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Darby\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-11-12\n                                    [endDate] => 2026-11-11\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 4\n                                    [subscription] => Array\n                                        (\n                                            [id] => 63481682348309\n                                            [quantity] => 4\n                                            [endDate] => 2025-11-11\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 1640\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 1640\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1640\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-13\n                                                            [startDate] => 2025-11-12\n                                                            [endDate] => 2026-11-11\n                                                            [extendedSRP] => 1640\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 1640\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1001027\n)\n
[quote_update] [2025-08-13 08:55:17] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Wed, 13 Aug 2025 08:55:17 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3718\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 5bd4882c-0b00-4067-a554-3f30cb74fe88\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PPHmWFryIAMEK3g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689c52f5-4fc16124238a2846423fcb1c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1001027\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-13T09:55:10+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1640\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 1640\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1640\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AIR HANDLING SYSTEMS\n                            [addressLine1] => Unit 3-5 Furnace Industrial Estate\n                            [city] => Shildon\n                            [stateProvinceCode] => \n                            [stateProvince] => COUNTY DURHAM\n                            [postalCode] => DL4 1QB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Darby\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-11-12\n                                    [endDate] => 2026-11-11\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 4\n                                    [subscription] => Array\n                                        (\n                                            [id] => 63481682348309\n                                            [quantity] => 4\n                                            [endDate] => 2025-11-11\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 1640\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 1640\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1640\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-13\n                                                            [startDate] => 2025-11-12\n                                                            [endDate] => 2026-11-11\n                                                            [extendedSRP] => 1640\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 1640\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1001027\n)\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 08:58:19
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => abb5d950-f3f9-4ffd-9df3-f99ccd679923\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001027\n            [transactionId] => ac02bb63-e033-5b29-a6e6-da75b5181708\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1001027 status changed to Quoted.\n            [modifiedAt] => 2025-08-13T08:58:16.391Z\n        )\n\n    [publishedAt] => 2025-08-13T08:58:16.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 08:58:19
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => abb5d950-f3f9-4ffd-9df3-f99ccd679923\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001027\n            [transactionId] => ac02bb63-e033-5b29-a6e6-da75b5181708\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1001027 status changed to Quoted.\n            [modifiedAt] => 2025-08-13T08:58:16.391Z\n        )\n\n    [publishedAt] => 2025-08-13T08:58:16.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-13 08:58:19] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1001027', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001027', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-13 09:16:25] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 09:16:25
[quote_update] [2025-08-13 09:16:25] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 09:16:25] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7917c623-a7f6-4d9f-98fd-2416763d7194\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 04cce22a-9bb5-5a6e-95ca-833cc1af26ac\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-13T09:16:22.634Z\n        )\n\n    [publishedAt] => 2025-08-13T09:16:22.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 09:16:25] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 09:16:25
[quote_update] [2025-08-13 09:16:25] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 09:16:25] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7917c623-a7f6-4d9f-98fd-2416763d7194\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => 04cce22a-9bb5-5a6e-95ca-833cc1af26ac\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-08-13T09:16:22.634Z\n        )\n\n    [publishedAt] => 2025-08-13T09:16:22.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 09:16:26] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Wed, 13 Aug 2025 09:16:26 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1720\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c4449e74-bd23-42f8-8afe-eb04f6db61d4\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PPKssEuloAMEYpg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689c57ea-411a71fa5312eb894e93d296\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-13T10:16:21+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => 247 INTERIORS\n                            [addressLine1] => Unit 31 Park Farm Industrial Estate\n                            [addressLine2] => Ermine Street\n                            [city] => Buntingford\n                            [stateProvinceCode] => \n                            [stateProvince] => HERTFORDSHIRE\n                            [postalCode] => SG9 9AZ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Matt\n                            [lastName] => Godfrey\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-13 09:16:27] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Wed, 13 Aug 2025 09:16:27 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1621\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 6e2de44b-fcb2-487a-b419-9eeb193d5113\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PPKssHrUIAMEuBg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689c57ea-157ae193165a38c1300dadc1\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-13T10:16:21+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => 247 INTERIORS\n                            [addressLine1] => Unit 31 Park Farm Industrial Estate\n                            [addressLine2] => Ermine Street\n                            [city] => Buntingford\n                            [stateProvinceCode] => \n                            [stateProvince] => HERTFORDSHIRE\n                            [postalCode] => SG9 9AZ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Matt\n                            [lastName] => Godfrey\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-08-13 09:53:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 09:53:22
[quote_update] [2025-08-13 09:53:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 09:53:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d9e8df26-fd65-4282-a464-1587f9f81d44\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001473\n            [transactionId] => b30e0409-b179-5297-9d01-651fd3b9bf9a\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1001473 status changed to Draft.\n            [modifiedAt] => 2025-08-13T09:53:19.939Z\n        )\n\n    [publishedAt] => 2025-08-13T09:53:20.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 09:53:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 09:53:22
[quote_update] [2025-08-13 09:53:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 09:53:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d9e8df26-fd65-4282-a464-1587f9f81d44\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001473\n            [transactionId] => b30e0409-b179-5297-9d01-651fd3b9bf9a\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1001473 status changed to Draft.\n            [modifiedAt] => 2025-08-13T09:53:19.939Z\n        )\n\n    [publishedAt] => 2025-08-13T09:53:20.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 09:53:24] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Wed, 13 Aug 2025 09:53:24 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 84add87e-2d83-42a6-8d44-38ac2f3ed613\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PPQHLGVYIAMEkqg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689c6093-0b02251329056a06019a71ee\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1001473\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-13T10:53:18+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => EMPIRE INTERIOR CONTRACTORS\n                            [addressLine1] => 102 Chatsworth Drive\n                            [city] => Mansfield\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG18 4QX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Luke\n                            [lastName] => Pella\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-28\n                                    [endDate] => 2026-08-27\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56699607834629\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-27\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-13\n                                                            [startDate] => 2025-08-28\n                                                            [endDate] => 2026-08-27\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1001473\n)\n
[quote_update] [2025-08-13 09:53:24] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Wed, 13 Aug 2025 09:53:24 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 89feddc0-c3ed-4b5a-8880-bed41fce33ae\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PPQHLGEKIAMEiMw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689c6093-00fc19b369dbdf4f2a3beff6\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1001473\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-13T10:53:18+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => EMPIRE INTERIOR CONTRACTORS\n                            [addressLine1] => 102 Chatsworth Drive\n                            [city] => Mansfield\n                            [stateProvinceCode] => \n                            [stateProvince] => NOTTINGHAMSHIRE\n                            [postalCode] => NG18 4QX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Luke\n                            [lastName] => Pella\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-28\n                                    [endDate] => 2026-08-27\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56699607834629\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-27\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-13\n                                                            [startDate] => 2025-08-28\n                                                            [endDate] => 2026-08-27\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1001473\n)\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 09:54:38
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 436673ac-365d-44bf-b051-548c63e73593\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001473\n            [transactionId] => b30e0409-b179-5297-9d01-651fd3b9bf9a\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1001473 status changed to Quoted.\n            [modifiedAt] => 2025-08-13T09:54:35.904Z\n        )\n\n    [publishedAt] => 2025-08-13T09:54:36.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-13 09:54:38
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 436673ac-365d-44bf-b051-548c63e73593\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1001473\n            [transactionId] => b30e0409-b179-5297-9d01-651fd3b9bf9a\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1001473 status changed to Quoted.\n            [modifiedAt] => 2025-08-13T09:54:35.904Z\n        )\n\n    [publishedAt] => 2025-08-13T09:54:36.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-13 09:54:38] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1001473', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1001473', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
