[customer_matching] [2025-08-11 08:42:53] [customers.api.php:162] Customer matching - Found 0 CSV entries to check
[customer_matching] [2025-08-11 08:42:53] [customers.api.php:163] Customer data: email=<EMAIL>, name=SHIRE GROUP BSC Ltd
[customer_matching] [2025-08-11 08:42:53] [customers.api.php:171] CSV entries by source table: []
[customer_matching] [2025-08-11 08:42:53] [customers.api.php:247] Final result: Found 0 matching CSV subscriptions
[customer_matching] [2025-08-11 13:02:29] [customers.api.php:162] Customer matching - Found 0 CSV entries to check
[customer_matching] [2025-08-11 13:02:29] [customers.api.php:163] Customer data: email=none, name=AZUMI Ltd
[customer_matching] [2025-08-11 13:02:29] [customers.api.php:171] CSV entries by source table: []
[customer_matching] [2025-08-11 13:02:29] [customers.api.php:247] Final result: Found 0 matching CSV subscriptions
[customer_matching] [2025-08-11 13:02:33] [customers.api.php:162] Customer matching - Found 0 CSV entries to check
[customer_matching] [2025-08-11 13:02:33] [customers.api.php:163] Customer data: email=none, name=Azumi Ltd
[customer_matching] [2025-08-11 13:02:33] [customers.api.php:171] CSV entries by source table: []
[customer_matching] [2025-08-11 13:02:33] [customers.api.php:247] Final result: Found 0 matching CSV subscriptions
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:162] Customer matching - Found 5 CSV entries to check
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:163] Customer data: email=<EMAIL>, name=BSBA TEES Ltd
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:171] CSV entries by source table: {"autobooks_import_bluebeam_data":4,"autobooks_import_sketchup_data":1}
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-11 14:23:22] [customers.api.php:247] Final result: Found 0 matching CSV subscriptions
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:162] Customer matching - Found 5 CSV entries to check
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:163] Customer data: email=<EMAIL>, name=BSBA TEES Ltd
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:171] CSV entries by source table: {"autobooks_import_bluebeam_data":4,"autobooks_import_sketchup_data":1}
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-11 14:23:45] [customers.api.php:247] Final result: Found 0 matching CSV subscriptions
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:162] Customer matching - Found 5 CSV entries to check
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:163] Customer data: email=<EMAIL>, name=BSBA TEES Ltd
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:171] CSV entries by source table: {"autobooks_import_bluebeam_data":4,"autobooks_import_sketchup_data":1}
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='cd engineering' (similarity: 26.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='clear routing solutions' (similarity: 18.75%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='k home international' (similarity: 20.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[endcust_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[end_customer_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[company_name]='bsba' (similarity: 61.************%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[sold_to_name]='td synnex united kingdom' (similarity: 24.242424242424%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:222] Comparing companies: customer='bsba tees' vs entry[vendor_name]='trimble' (similarity: 25%)
[customer_matching] [2025-08-11 14:24:36] [customers.api.php:247] Final result: Found 0 matching CSV subscriptions
