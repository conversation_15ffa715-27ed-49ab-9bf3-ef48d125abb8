[preview_debug] [2025-08-13 09:06:17] [data_sources.api.php:1419] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-13 09:06:17] [data_sources.api.php:1436] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-13 09:06:34] [data_sources.api.php:1419] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-13 09:06:34] [data_sources.api.php:1436] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
[preview_debug] [2025-08-13 09:28:28] [data_sources.api.php:1419] Array\n(\n    [function] => data_preview_fragment\n    [tables] => Array\n        (\n            [0] => autobooks_import_bluebeam_data\n        )\n\n    [joins] => Array\n        (\n        )\n\n    [selected_columns] => Array\n        (\n            [autobooks_import_bluebeam_data] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                )\n\n        )\n\n    [table_aliases] => Array\n        (\n        )\n\n)\n
[preview_debug] [2025-08-13 09:28:28] [data_sources.api.php:1436] Array\n(\n    [function] => data_preview_fragment\n    [generated_query] => SELECT `autobooks_import_bluebeam_data`.`id` AS `autobooks_import_bluebeam_data_id`, `autobooks_import_bluebeam_data`.`serial_number` AS `autobooks_import_bluebeam_data_serial_number`, `autobooks_import_bluebeam_data`.`name` AS `autobooks_import_bluebeam_data_name` FROM `autobooks_import_bluebeam_data` LIMIT 10\n)\n
