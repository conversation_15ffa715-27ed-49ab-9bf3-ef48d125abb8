[database_errors] [2025-08-13 09:55:47] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(111) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'autobooks_import_bluebeam_data.title' in 'where clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(71) "Unknown column 'autobooks_import_bluebeam_data.title' in 'where clause'"\n  ["query"]: string(230) "SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`title` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`description` LIKE '%bsba%')"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(22) "autobooks_data_sources"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(72) "/baffletrain/autocadlt/autobooks/api/system/data_table/data_table_filter"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1258) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(682): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(946): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_source_manager.class.php(496): system\database::rawQuery()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table.api.php(25): system\data_source_manager::get_data_source_data()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php(77): api\data_table\data_table_filter()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(211): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#9 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'autobooks_import_bluebeam_data.title' in 'where clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'autobooks_import_bluebeam_data.title' in 'where clause'","query":"SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`title` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`description` LIKE '%bsba%')","parameters":[],"table":"autobooks_data_sources","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/data_table\/data_table_filter","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(682): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(946): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_source_manager.class.php(496): system\\database::rawQuery()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/data_table.api.php(25): system\\data_source_manager::get_data_source_data()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php(77): api\\data_table\\data_table_filter()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(211): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#9 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'autobooks_import_bluebeam_data.title' in 'where clause'"]}\n         1: "SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`title` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`description` LIKE '%bsba%')"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 946\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM `autobooks_import_bluebeam_data` WHERE (`autobooks_import_bluebeam_data`.`name` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`title` LIKE '%bsba%' OR `autobooks_import_bluebeam_data`.`description` LIKE '%bsba%')"\n         1: []\n-->\n
