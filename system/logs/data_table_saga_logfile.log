[data_table_saga] [2025-08-13 08:47:03] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=, id=40
[data_table_saga] [2025-08-13 08:47:03] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=40
[data_table_saga] [2025-08-13 08:47:09] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 08:47:09] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 08:47:09] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 08:54:25] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 08:54:25] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 08:54:25] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:01:03] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:01:03] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:01:03] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:01:04] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:01:04] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:01:04] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:03:13] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=, id=40
[data_table_saga] [2025-08-13 09:03:13] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=40
[data_table_saga] [2025-08-13 09:03:19] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:03:19] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:03:19] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:05:15] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:05:15] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:05:15] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:05:16] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:05:16] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:05:16] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:09:03] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=, id=40
[data_table_saga] [2025-08-13 09:09:03] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=40
[data_table_saga] [2025-08-13 09:09:08] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:09:08] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:09:08] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:09:09] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:09:09] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:09:09] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:27:42] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-13 09:27:42\n            [updated_at] => 2025-08-13 09:27:42\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-13 09:27:48] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:27:48] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:27:50] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:27:50] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:27:53] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:27:53] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:27:53] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:27:55] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:27:55] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:27:55] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:27:56] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:27:56] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:27:56] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:28:00] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:28:00] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:28:00] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:28:04] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:28:04] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:28:04] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:30:29] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:30:29] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:30:33] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:30:33] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:30:33] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:31:10] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:31:10] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:31:50] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:31:50] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:31:55] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:31:55] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:31:55] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:35:52] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:35:52] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:35:56] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:35:56] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:35:56] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:36:33] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:36:33] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:36:33] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:44:36] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:44:36] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:44:36] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:44:37] [data-table.edge.php:59]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-13 09:44:37] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:44:37] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-13 09:55:43] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 09:55:43] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 10:01:35] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 10:01:35] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=41
[data_table_saga] [2025-08-13 10:01:39] [data_table.api.php:70]  Updated data source 41 with search columns: ["autobooks_import_bluebeam_data.serial_number","autobooks_import_bluebeam_data.name","autobooks_import_bluebeam_data.contract","autobooks_import_bluebeam_data.product_name","autobooks_import_bluebeam_data.quantity","autobooks_import_bluebeam_data.end_date","autobooks_import_bluebeam_data.account_primary_reseller_name","autobooks_import_bluebeam_data.order_po_number","autobooks_import_bluebeam_data.order_shipping_address","autobooks_import_bluebeam_data.order_shipping_city","autobooks_import_bluebeam_data.order_shipping_state_province","autobooks_import_bluebeam_data.order_shipping_country"]
[data_table_saga] [2025-08-13 10:01:41] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:01:41] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:01:43] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:01:43] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:00] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:00] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:02] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:02] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:04] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:04] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:05] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:02:05] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:03:24] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:03:24] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:03:27] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:03:27] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:03:40] [data-table.edge.php:156] Data table template: table=autobooks_import_bluebeam_data, type=data_source, id=41
[data_table_saga] [2025-08-13 10:03:40] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=data_source, id=41
