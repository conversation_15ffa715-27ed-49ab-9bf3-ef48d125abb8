[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => AIR HANDLING SYSTEMS\n    [:up_name] => AIR HANDLING SYSTEMS\n    [:address1] => Unit 3-5 Furnace Industrial Estate\n    [:up_address1] => Unit 3-5 Furnace Industrial Estate\n    [:city] => Shildon\n    [:up_city] => Shildon\n    [:postal_code] => DL4 1QB\n    [:up_postal_code] => DL4 1QB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => COUNTY DURHAM\n    [:up_state_province] => COUNTY DURHAM\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Jonathan\n    [:up_first_name] => Jonathan\n    [:last_name] => Darby\n    [:up_last_name] => Darby\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1001027\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-13T09:55:10+01:00\n    [:up_quote_created_time] => 2025-08-13T09:55:10+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 1640\n    [:up_total_list_amount] => 1640\n    [:total_net_amount] => 1640\n    [:up_total_net_amount] => 1640\n    [:total_amount] => 1640\n    [:up_total_amount] => 1640\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3602\n    [:up_end_customer] => 3602\n    [:quote_contact] => 18738\n    [:up_quote_contact] => 18738\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 4\n    [:up_quantity] => 4\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-11-12\n    [:up_start_date] => 2025-11-12\n    [:end_date] => 2026-11-11\n    [:up_end_date] => 2026-11-11\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 1640\n    [:up_extended_srp] => 1640\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 1640\n    [:up_end_user_price] => 1640\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 63481682348309\n    [:up_subscription_id] => 63481682348309\n    [:subscription_quantity] => 4\n    [:up_subscription_quantity] => 4\n    [:subscription_endDate] => 2025-11-11\n    [:up_subscription_endDate] => 2025-11-11\n    [:quote_id] => 2043\n    [:up_quote_id] => 2043\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => AIR HANDLING SYSTEMS\n    [:up_name] => AIR HANDLING SYSTEMS\n    [:address1] => Unit 3-5 Furnace Industrial Estate\n    [:up_address1] => Unit 3-5 Furnace Industrial Estate\n    [:city] => Shildon\n    [:up_city] => Shildon\n    [:postal_code] => DL4 1QB\n    [:up_postal_code] => DL4 1QB\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => COUNTY DURHAM\n    [:up_state_province] => COUNTY DURHAM\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => *********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Jonathan\n    [:up_first_name] => Jonathan\n    [:last_name] => Darby\n    [:up_last_name] => Darby\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1001027\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-13T09:55:10+01:00\n    [:up_quote_created_time] => 2025-08-13T09:55:10+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 1640\n    [:up_total_list_amount] => 1640\n    [:total_net_amount] => 1640\n    [:up_total_net_amount] => 1640\n    [:total_amount] => 1640\n    [:up_total_amount] => 1640\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3602\n    [:up_end_customer] => 3602\n    [:quote_contact] => 18738\n    [:up_quote_contact] => 18738\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 4\n    [:up_quantity] => 4\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-11-12\n    [:up_start_date] => 2025-11-12\n    [:end_date] => 2026-11-11\n    [:up_end_date] => 2026-11-11\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 1640\n    [:up_extended_srp] => 1640\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 1640\n    [:up_end_user_price] => 1640\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 63481682348309\n    [:up_subscription_id] => 63481682348309\n    [:subscription_quantity] => 4\n    [:up_subscription_quantity] => 4\n    [:subscription_endDate] => 2025-11-11\n    [:up_subscription_endDate] => 2025-11-11\n    [:quote_id] => 2043\n    [:up_quote_id] => 2043\n)\n
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-13 08:55:17] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => 247 INTERIORS\n    [:up_name] => 247 INTERIORS\n    [:address1] => Unit 31 Park Farm Industrial Estate\n    [:up_address1] => Unit 31 Park Farm Industrial Estate\n    [:address2] => Ermine Street\n    [:up_address2] => Ermine Street\n    [:city] => Buntingford\n    [:up_city] => Buntingford\n    [:postal_code] => SG9 9AZ\n    [:up_postal_code] => SG9 9AZ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => HERTFORDSHIRE\n    [:up_state_province] => HERTFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Matt\n    [:up_first_name] => Matt\n    [:last_name] => Godfrey\n    [:up_last_name] => Godfrey\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account, admin = :admin ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account, admin = :up_admin;
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1001151\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-13T10:16:21+01:00\n    [:up_quote_created_time] => 2025-08-13T10:16:21+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 4394\n    [:up_end_customer] => 4394\n    [:quote_contact] => 17962\n    [:up_quote_contact] => 17962\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n    [:admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n    [:up_admin] => {"contactCsn":null,"email":null,"firstName":null,"lastName":null,"phone":null,"preferredLanguage":"en"}\n)\n
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-13 09:16:26] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => 247 INTERIORS\n    [:up_name] => 247 INTERIORS\n    [:address1] => Unit 31 Park Farm Industrial Estate\n    [:up_address1] => Unit 31 Park Farm Industrial Estate\n    [:address2] => Ermine Street\n    [:up_address2] => Ermine Street\n    [:city] => Buntingford\n    [:up_city] => Buntingford\n    [:postal_code] => SG9 9AZ\n    [:up_postal_code] => SG9 9AZ\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => HERTFORDSHIRE\n    [:up_state_province] => HERTFORDSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Matt\n    [:up_first_name] => Matt\n    [:last_name] => Godfrey\n    [:up_last_name] => Godfrey\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1001151\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-13T10:16:21+01:00\n    [:up_quote_created_time] => 2025-08-13T10:16:21+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 0\n    [:up_total_list_amount] => 0\n    [:total_net_amount] => 0\n    [:up_total_net_amount] => 0\n    [:total_amount] => 0\n    [:up_total_amount] => 0\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 4394\n    [:up_end_customer] => 4394\n    [:quote_contact] => 17962\n    [:up_quote_contact] => 17962\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:267] Skipping line_item - array is empty at path: lineItems
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:267] Skipping ref_subs - array is empty at path: lineItems
[data_importer] [2025-08-13 09:16:27] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1381] import_csv_to_hilt_table called with table: autobooks_import_bluebeam_data, is_file_path: true
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1393] Using file path: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755077260.csv
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1397] Calling import_csv_with_auto_schema for table: autobooks_import_bluebeam_data
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1432] Analyzing CSV structure for: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755077260.csv
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755077260.csv
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1438] CSV analysis completed successfully
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1441] Generating enhanced schema for table: autobooks_import_bluebeam_data
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1447] Schema generation completed successfully
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1299] Successfully created table: autobooks_import_bluebeam_data
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:214] Starting import of /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1755077260.csv
[data_importer] [2025-08-13 09:27:41] [data_importer.class.php:1520] Starting typed column import for table: autobooks_import_bluebeam_data
[data_importer] [2025-08-13 09:27:42] [data_importer.class.php:1557] Typed column import completed: 317 success, 172 failed
[data_importer] [2025-08-13 09:27:42] [data_importer.class.php:1475] Generating and storing table configuration for: autobooks_import_bluebeam_data
[data_importer] [2025-08-13 09:27:42] [data_importer.class.php:1669] Successfully generated and stored table configuration for: autobooks_import_bluebeam_data
[data_importer] [2025-08-13 09:27:42] [data_importer.class.php:1477] Table configuration result: {"success":true,"message":"Table configuration generated and stored successfully","config":{"title":"Autobooks import bluebeam data","description":"Auto-generated table from CSV import (489 rows, 100 analyzed)","items":[{"id":1,"serial_number":"**********","name":"Big C Engineering Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"08\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"2 NIGHTINGALE CLOSE,Bristol,UKK,BS36 2HB,GB","order_shipping_city":"Bristol","order_shipping_state_province":"UKK","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":2,"serial_number":"SN-0700749","name":"Rock Internet Ltd","contract":"********","product_name":"Core","quantity":"14","end_date":"09\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"64334\/E","order_shipping_address":"Kemp House 152-160 City Road,London,EC1V 2NX,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":3,"serial_number":"SN-0700749","name":"Rock Internet Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"09\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"64334\/E","order_shipping_address":"Kemp House 152-160 City Road,London,EC1V 2NX,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":4,"serial_number":"SN-0701451","name":"EDM-London","contract":"********","product_name":"Core","quantity":"5","end_date":"12\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62751\/A","order_shipping_address":"27 Kelso Place,London,W8 5QG,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":5,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"2","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":6,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"8","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":7,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"3","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":8,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"1","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":9,"serial_number":"**********","name":"Cooper Homewood Limited","contract":"********","product_name":"Core","quantity":"2","end_date":"23\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62869\/A","order_shipping_address":"1600 Parkway, Solent Business Park, White,Fareham,PO15 7AH,GB","order_shipping_city":"Fareham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":10,"serial_number":"**********","name":"Sawcon Construction Services Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"03\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62942\/A","order_shipping_address":"67 Birmingham Road,Walsall,WS9 0AJ,GB","order_shipping_city":"Walsall","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":11,"serial_number":"SN-0717315","name":"Cooper Homewood Limited","contract":"********","product_name":"Core","quantity":"1","end_date":"06\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62958\/A","order_shipping_address":"1600 Parkway, Solent Business Park, White,Fareham,PO15 7AH,GB","order_shipping_city":"Fareham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":12,"serial_number":"SN-0460872","name":"VKE Contractors Ltd","contract":"********","product_name":"Core","quantity":"2","end_date":"07\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62788\/A","order_shipping_address":"50-52 Wharf Road,London,N1 7EU,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":13,"serial_number":"SN-0462248","name":"T-Prime Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"08\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62887\/A","order_shipping_address":"Unit 3 Brampton Hall Farm,Little Bentley,CO7 8TA,GB","order_shipping_city":"Little Bentley","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":14,"serial_number":"SN-0720533","name":"Fabric Building Physics Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"12\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"61889\/A-1","order_shipping_address":"42 Dibdin View,Bridport,DT6 5FA,GB","order_shipping_city":"Bridport","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":15,"serial_number":"SN-0721664","name":"VKE Contractors Ltd","contract":"********","product_name":"Complete","quantity":"1","end_date":"13\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"3 Lakeside Crescent BRENTWOOD ESSEX,Brentwood,UKH,CM144JB,GB","order_shipping_city":"Brentwood","order_shipping_state_province":"UKH","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":16,"serial_number":"SN-0299694","name":"Metway Electrical industries","contract":"********","product_name":"Subscription Renewal: Revu Extreme to Complete","quantity":"3","end_date":"14\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62877\/A","order_shipping_address":"Barrie House, 18 North St,Brighton,OKJ,BN41 1DG,GB","order_shipping_city":"Brighton","order_shipping_state_province":"OKJ","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":17,"serial_number":"SN-0378373","name":"Metway Electrical industries","contract":"********","product_name":"Complete","quantity":"3","end_date":"14\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62877\/A","order_shipping_address":"Barrie House, 18 North St,Brighton,OKJ,BN41 1DG,GB","order_shipping_city":"Brighton","order_shipping_state_province":"OKJ","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":18,"serial_number":"SN-0723217","name":"Countrywide Ceilings & Partitions Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"17\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63011\/A-1","order_shipping_address":"708, Walmersley road,,Bury,BL9 6RN,GB","order_shipping_city":"Bury","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":19,"serial_number":"SN-0725314","name":"Aura Homes Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"20\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63043\/A","order_shipping_address":"7 Prescott Place,Clapham,SW4 6BS,GB","order_shipping_city":"Clapham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":20,"serial_number":"SN-0473078","name":"Aden Contracting","contract":"********","product_name":"Core","quantity":"2","end_date":"20\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62845\/A","order_shipping_address":"Cambridge Road,Bedford,MK42 0LH,GB","order_shipping_city":"Bedford","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":21,"serial_number":"SN-0725445","name":"Keldin Engineering Limited","contract":"********","product_name":"Complete","quantity":"1","end_date":"20\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"39,MAIDENHEAD,JS,SL6 5DF,GB","order_shipping_city":"MAIDENHEAD","order_shipping_state_province":"JS","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":22,"serial_number":"SN-0728653","name":"Bailiss & Co","contract":"********","product_name":"Core","quantity":"1","end_date":"26\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63104\/A","order_shipping_address":"24 Danby Street,London,SE15 4BU,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":23,"serial_number":"SN-0477396","name":"PLS Civil Engineering Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"26\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63087\/A","order_shipping_address":"Blacksmith Way,Shirebrook,NG20 8RJ,GB","order_shipping_city":"Shirebrook","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":24,"serial_number":"SN-0731320","name":"Arcade Tiling Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"01\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63140","order_shipping_address":"61 Crowstone Road,Westcliff-on-Sea,SS0 8BG,GB","order_shipping_city":"Westcliff-on-Sea","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":25,"serial_number":"SN-0485293","name":"Countrywide Ceilings & Partitions Ltd","contract":"********","product_name":"Complete","quantity":"1","end_date":"02\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62992\/A","order_shipping_address":"Unit 8, Spring Business Park,Bolton,BL3 6EJ,GB","order_shipping_city":"Bolton","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":26,"serial_number":"SN-0732884","name":"Planet Partitioning \u2013 South Division","contract":"********","product_name":"Complete","quantity":"1","end_date":"04\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63171\/F","order_shipping_address":"Radii Planet House, Edward Way,Burgess Hill,RH15 9TZ,GB","order_shipping_city":"Burgess Hill","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":27,"serial_number":"SN-0487612","name":"Cooper Homewood Limited","contract":"********","product_name":"Core","quantity":"1","end_date":"05\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62815\/A","order_shipping_address":"1600 Parkway, Solent Business Park, White,Fareham,PO15 7AH,GB","order_shipping_city":"Fareham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":28,"serial_number":"SN-0488507","name":"Root Group Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"06\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63057\/F","order_shipping_address":"Freedom House,Cheltenham,GL51 9TU,GB","order_shipping_city":"Cheltenham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":29,"serial_number":null,"name":"JJ Sweeney Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"07\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63184\/A","order_shipping_address":"Conqueror Court, 3, Spilsby Rd,Romford,RM3 8JS,GB","order_shipping_city":"Romford","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":30,"serial_number":"SN-0734418","name":"HR Wallingford Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"08\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"37 Rowlock Gardens,Thatcham,OKJ,RG18 9WT,GB","order_shipping_city":"Thatcham","order_shipping_state_province":"OKJ","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"}],"columns":[{"label":"Serial Number","field":"serial_number","filter":true,"sortable":true,"extra_parameters":""},{"label":"Company Name","field":"name","filter":true,"sortable":true,"extra_parameters":""},{"label":"Numeric ID","field":"contract","filter":true,"sortable":true,"extra_parameters":""},{"label":"Product Name","field":"product_name","filter":true,"sortable":true,"extra_parameters":""},{"label":"Quantity","field":"quantity","filter":true,"sortable":true,"extra_parameters":""},{"label":"End Date","field":"end_date","filter":true,"sortable":true,"extra_parameters":""},{"label":"Company Name","field":"account_primary_reseller_name","filter":true,"sortable":true,"extra_parameters":""},{"label":"Order Shipping Address","field":"order_shipping_address","filter":true,"sortable":true,"extra_parameters":""}],"available_fields":["id","serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country","created_at","updated_at"],"rows":{"id_prefix":"row_","id_field":"id","class_postfix":"","extra_parameters":""},"just_body":false,"just_rows":false,"items_per_page":30,"current_page_num":1,"sort_column":"","sort_direction":"asc","callback":"","class":""},"route_key":"enhanced_autobooks_import_bluebeam_data"}
[data_importer] [2025-08-13 09:27:42] [data_importer.class.php:1399] import_csv_with_auto_schema returned: {"success":true,"message":"CSV imported with auto-generated schema and stored configuration","table_name":"autobooks_import_bluebeam_data","analysis":{"success":true,"headers":["\ufeffSerial Number","Name","Contract","Product Name","Quantity","End Date","Account Primary Reseller Name","Order PO Number","Order Shipping Address","Order Shipping City","Order Shipping State\/Province","Order Shipping Country"],"data_types":{"\ufeffSerial Number":"string","Name":"string","Contract":"string","Product Name":"string","Quantity":"string","End Date":"string","Account Primary Reseller Name":"string","Order PO Number":"string","Order Shipping Address":"string","Order Shipping City":"string","Order Shipping State\/Province":"string","Order Shipping Country":"string"},"sample_data":{"\ufeffSerial Number":["**********","SN-0700749","SN-0700749","SN-0701451","SN-0702276"],"Name":["Big C Engineering Ltd","Rock Internet Ltd","Rock Internet Ltd","EDM-London","Erith"],"Contract":["********","********","********","********","********"],"Product Name":["Core","Core","Core","Core","Core"],"Quantity":["1","14","1","5","1"],"End Date":["08\/05\/2025","09\/05\/2025","09\/05\/2025","12\/05\/2025","13\/05\/2025"],"Account Primary Reseller Name":["TCS CAD & BIM Solutions Ltd.","TCS CAD & BIM Solutions Ltd.","TCS CAD & BIM Solutions Ltd.","TCS CAD & BIM Solutions Ltd.","TCS CAD & BIM Solutions Ltd."],"Order PO Number":["","64334\/E","64334\/E","62751\/A","62763\/F"],"Order Shipping Address":["2 NIGHTINGALE CLOSE,Bristol,UKK,BS36 2HB,GB","Kemp House 152-160 City Road,London,EC1V 2NX,GB","Kemp House 152-160 City Road,London,EC1V 2NX,GB","27 Kelso Place,London,W8 5QG,GB","Phil Jones\n"],"Order Shipping City":["Bristol","London","London","London","Middlesborough"],"Order Shipping State\/Province":["UKK","","","","UKC"],"Order Shipping Country":["GB","GB","GB","GB","GB"]},"total_rows":489,"analyzed_rows":100},"schema":{"table_name":"autobooks_import_bluebeam_data","primary_key":"id","columns":{"id":{"type":"increments","nullable":false},"serial_number":{"type":"string","length":255,"nullable":true},"name":{"type":"string","length":255,"nullable":true},"contract":{"type":"string","length":255,"nullable":true},"product_name":{"type":"string","length":255,"nullable":true},"quantity":{"type":"string","length":255,"nullable":true},"end_date":{"type":"string","length":255,"nullable":true},"account_primary_reseller_name":{"type":"string","length":255,"nullable":true},"order_po_number":{"type":"string","length":255,"nullable":true},"order_shipping_address":{"type":"string","length":255,"nullable":true},"order_shipping_city":{"type":"string","length":255,"nullable":true},"order_shipping_state_province":{"type":"string","length":255,"nullable":true},"order_shipping_country":{"type":"string","length":255,"nullable":true},"created_at":{"type":"timestamp","default":"CURRENT_TIMESTAMP"},"updated_at":{"type":"timestamp","default":"CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"}},"mapping":{"main":{"table":"autobooks_import_bluebeam_data","key":"id","columns":{"\ufeffSerial Number":"serial_number","Name":"name","Contract":"contract","Product Name":"product_name","Quantity":"quantity","End Date":"end_date","Account Primary Reseller Name":"account_primary_reseller_name","Order PO Number":"order_po_number","Order Shipping Address":"order_shipping_address","Order Shipping City":"order_shipping_city","Order Shipping State\/Province":"order_shipping_state_province","Order Shipping Country":"order_shipping_country"},"data_types":{"serial_number":"string","name":"string","contract":"string","product_name":"string","quantity":"string","end_date":"string","account_primary_reseller_name":"string","order_po_number":"string","order_shipping_address":"string","order_shipping_city":"string","order_shipping_state_province":"string","order_shipping_country":"string"}}}},"import_result":{"success":true,"message":"Successfully imported 317 rows into typed columns","imported_count":317,"failed_count":172,"table_name":"autobooks_import_bluebeam_data"},"config_result":{"success":true,"message":"Table configuration generated and stored successfully","config":{"title":"Autobooks import bluebeam data","description":"Auto-generated table from CSV import (489 rows, 100 analyzed)","items":[{"id":1,"serial_number":"**********","name":"Big C Engineering Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"08\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"2 NIGHTINGALE CLOSE,Bristol,UKK,BS36 2HB,GB","order_shipping_city":"Bristol","order_shipping_state_province":"UKK","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":2,"serial_number":"SN-0700749","name":"Rock Internet Ltd","contract":"********","product_name":"Core","quantity":"14","end_date":"09\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"64334\/E","order_shipping_address":"Kemp House 152-160 City Road,London,EC1V 2NX,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":3,"serial_number":"SN-0700749","name":"Rock Internet Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"09\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"64334\/E","order_shipping_address":"Kemp House 152-160 City Road,London,EC1V 2NX,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":4,"serial_number":"SN-0701451","name":"EDM-London","contract":"********","product_name":"Core","quantity":"5","end_date":"12\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62751\/A","order_shipping_address":"27 Kelso Place,London,W8 5QG,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":5,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"2","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":6,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"8","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":7,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"3","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":8,"serial_number":"**********","name":"Lionweld Kennedy Group","contract":"********","product_name":"Core","quantity":"1","end_date":"19\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63933\/F","order_shipping_address":"Marsh Road,Middlesborough,UKC,TS1 5JS,GB","order_shipping_city":"Middlesborough","order_shipping_state_province":"UKC","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":9,"serial_number":"**********","name":"Cooper Homewood Limited","contract":"********","product_name":"Core","quantity":"2","end_date":"23\/05\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62869\/A","order_shipping_address":"1600 Parkway, Solent Business Park, White,Fareham,PO15 7AH,GB","order_shipping_city":"Fareham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":10,"serial_number":"**********","name":"Sawcon Construction Services Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"03\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62942\/A","order_shipping_address":"67 Birmingham Road,Walsall,WS9 0AJ,GB","order_shipping_city":"Walsall","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":11,"serial_number":"SN-0717315","name":"Cooper Homewood Limited","contract":"********","product_name":"Core","quantity":"1","end_date":"06\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62958\/A","order_shipping_address":"1600 Parkway, Solent Business Park, White,Fareham,PO15 7AH,GB","order_shipping_city":"Fareham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":12,"serial_number":"SN-0460872","name":"VKE Contractors Ltd","contract":"********","product_name":"Core","quantity":"2","end_date":"07\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62788\/A","order_shipping_address":"50-52 Wharf Road,London,N1 7EU,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":13,"serial_number":"SN-0462248","name":"T-Prime Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"08\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62887\/A","order_shipping_address":"Unit 3 Brampton Hall Farm,Little Bentley,CO7 8TA,GB","order_shipping_city":"Little Bentley","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":14,"serial_number":"SN-0720533","name":"Fabric Building Physics Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"12\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"61889\/A-1","order_shipping_address":"42 Dibdin View,Bridport,DT6 5FA,GB","order_shipping_city":"Bridport","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":15,"serial_number":"SN-0721664","name":"VKE Contractors Ltd","contract":"********","product_name":"Complete","quantity":"1","end_date":"13\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"3 Lakeside Crescent BRENTWOOD ESSEX,Brentwood,UKH,CM144JB,GB","order_shipping_city":"Brentwood","order_shipping_state_province":"UKH","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":16,"serial_number":"SN-0299694","name":"Metway Electrical industries","contract":"********","product_name":"Subscription Renewal: Revu Extreme to Complete","quantity":"3","end_date":"14\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62877\/A","order_shipping_address":"Barrie House, 18 North St,Brighton,OKJ,BN41 1DG,GB","order_shipping_city":"Brighton","order_shipping_state_province":"OKJ","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":17,"serial_number":"SN-0378373","name":"Metway Electrical industries","contract":"********","product_name":"Complete","quantity":"3","end_date":"14\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62877\/A","order_shipping_address":"Barrie House, 18 North St,Brighton,OKJ,BN41 1DG,GB","order_shipping_city":"Brighton","order_shipping_state_province":"OKJ","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":18,"serial_number":"SN-0723217","name":"Countrywide Ceilings & Partitions Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"17\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63011\/A-1","order_shipping_address":"708, Walmersley road,,Bury,BL9 6RN,GB","order_shipping_city":"Bury","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":19,"serial_number":"SN-0725314","name":"Aura Homes Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"20\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63043\/A","order_shipping_address":"7 Prescott Place,Clapham,SW4 6BS,GB","order_shipping_city":"Clapham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":20,"serial_number":"SN-0473078","name":"Aden Contracting","contract":"********","product_name":"Core","quantity":"2","end_date":"20\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62845\/A","order_shipping_address":"Cambridge Road,Bedford,MK42 0LH,GB","order_shipping_city":"Bedford","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":21,"serial_number":"SN-0725445","name":"Keldin Engineering Limited","contract":"********","product_name":"Complete","quantity":"1","end_date":"20\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"39,MAIDENHEAD,JS,SL6 5DF,GB","order_shipping_city":"MAIDENHEAD","order_shipping_state_province":"JS","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":22,"serial_number":"SN-0728653","name":"Bailiss & Co","contract":"********","product_name":"Core","quantity":"1","end_date":"26\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63104\/A","order_shipping_address":"24 Danby Street,London,SE15 4BU,GB","order_shipping_city":"London","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":23,"serial_number":"SN-0477396","name":"PLS Civil Engineering Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"26\/06\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63087\/A","order_shipping_address":"Blacksmith Way,Shirebrook,NG20 8RJ,GB","order_shipping_city":"Shirebrook","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":24,"serial_number":"SN-0731320","name":"Arcade Tiling Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"01\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63140","order_shipping_address":"61 Crowstone Road,Westcliff-on-Sea,SS0 8BG,GB","order_shipping_city":"Westcliff-on-Sea","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":25,"serial_number":"SN-0485293","name":"Countrywide Ceilings & Partitions Ltd","contract":"********","product_name":"Complete","quantity":"1","end_date":"02\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62992\/A","order_shipping_address":"Unit 8, Spring Business Park,Bolton,BL3 6EJ,GB","order_shipping_city":"Bolton","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":26,"serial_number":"SN-0732884","name":"Planet Partitioning \u2013 South Division","contract":"********","product_name":"Complete","quantity":"1","end_date":"04\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63171\/F","order_shipping_address":"Radii Planet House, Edward Way,Burgess Hill,RH15 9TZ,GB","order_shipping_city":"Burgess Hill","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":27,"serial_number":"SN-0487612","name":"Cooper Homewood Limited","contract":"********","product_name":"Core","quantity":"1","end_date":"05\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"62815\/A","order_shipping_address":"1600 Parkway, Solent Business Park, White,Fareham,PO15 7AH,GB","order_shipping_city":"Fareham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":28,"serial_number":"SN-0488507","name":"Root Group Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"06\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63057\/F","order_shipping_address":"Freedom House,Cheltenham,GL51 9TU,GB","order_shipping_city":"Cheltenham","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":29,"serial_number":null,"name":"JJ Sweeney Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"07\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":"63184\/A","order_shipping_address":"Conqueror Court, 3, Spilsby Rd,Romford,RM3 8JS,GB","order_shipping_city":"Romford","order_shipping_state_province":null,"order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"},{"id":30,"serial_number":"SN-0734418","name":"HR Wallingford Ltd","contract":"********","product_name":"Core","quantity":"1","end_date":"08\/07\/2025","account_primary_reseller_name":"TCS CAD & BIM Solutions Ltd.","order_po_number":null,"order_shipping_address":"37 Rowlock Gardens,Thatcham,OKJ,RG18 9WT,GB","order_shipping_city":"Thatcham","order_shipping_state_province":"OKJ","order_shipping_country":"GB","created_at":"2025-08-13 09:27:41","updated_at":"2025-08-13 09:27:41"}],"columns":[{"label":"Serial Number","field":"serial_number","filter":true,"sortable":true,"extra_parameters":""},{"label":"Company Name","field":"name","filter":true,"sortable":true,"extra_parameters":""},{"label":"Numeric ID","field":"contract","filter":true,"sortable":true,"extra_parameters":""},{"label":"Product Name","field":"product_name","filter":true,"sortable":true,"extra_parameters":""},{"label":"Quantity","field":"quantity","filter":true,"sortable":true,"extra_parameters":""},{"label":"End Date","field":"end_date","filter":true,"sortable":true,"extra_parameters":""},{"label":"Company Name","field":"account_primary_reseller_name","filter":true,"sortable":true,"extra_parameters":""},{"label":"Order Shipping Address","field":"order_shipping_address","filter":true,"sortable":true,"extra_parameters":""}],"available_fields":["id","serial_number","name","contract","product_name","quantity","end_date","account_primary_reseller_name","order_po_number","order_shipping_address","order_shipping_city","order_shipping_state_province","order_shipping_country","created_at","updated_at"],"rows":{"id_prefix":"row_","id_field":"id","class_postfix":"","extra_parameters":""},"just_body":false,"just_rows":false,"items_per_page":30,"current_page_num":1,"sort_column":"","sort_direction":"asc","callback":"","class":""},"route_key":"enhanced_autobooks_import_bluebeam_data"}}
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, address2 = :address2, city = :city, postal_code = :postal_code, country_code = :country_code ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, address2 = :up_address2, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => TCS CAD & BIM Solutions Limited\n    [:up_name] => TCS CAD & BIM Solutions Limited\n    [:address1] => Unit F, Yorkway\n    [:up_address1] => Unit F, Yorkway\n    [:address2] => Mandale Ind Est\n    [:up_address2] => Mandale Ind Est\n    [:city] => Stockton On Tees\n    [:up_city] => Stockton On Tees\n    [:postal_code] => TS17 6BX\n    [:up_postal_code] => TS17 6BX\n    [:country_code] => GB\n    [:up_country_code] => GB\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => EMPIRE INTERIOR CONTRACTORS\n    [:up_name] => EMPIRE INTERIOR CONTRACTORS\n    [:address1] => 102 Chatsworth Drive\n    [:up_address1] => 102 Chatsworth Drive\n    [:city] => Mansfield\n    [:up_city] => Mansfield\n    [:postal_code] => NG18 4QX\n    [:up_postal_code] => NG18 4QX\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => NOTTINGHAMSHIRE\n    [:up_state_province] => NOTTINGHAMSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), account_csn = :up_account_csn, email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:up_account_csn] => **********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Emily\n    [:up_first_name] => Emily\n    [:last_name] => Wood\n    [:up_last_name] => Wood\n    [:phone] => +01642 677582\n    [:up_phone] => +01642 677582\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [account_type] => customer\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, name = :name, address1 = :address1, city = :city, postal_code = :postal_code, country_code = :country_code, state_province = :state_province, individual_flag = :individual_flag, account_type = :account_type ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), name = :up_name, address1 = :up_address1, city = :up_city, postal_code = :up_postal_code, country_code = :up_country_code, state_province = :up_state_province, individual_flag = :up_individual_flag, account_type = :up_account_type;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Luke\n    [:up_first_name] => Luke\n    [:last_name] => Pella\n    [:up_last_name] => Pella\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => **********\n    [:name] => EMPIRE INTERIOR CONTRACTORS\n    [:up_name] => EMPIRE INTERIOR CONTRACTORS\n    [:address1] => 102 Chatsworth Drive\n    [:up_address1] => 102 Chatsworth Drive\n    [:city] => Mansfield\n    [:up_city] => Mansfield\n    [:postal_code] => NG18 4QX\n    [:up_postal_code] => NG18 4QX\n    [:country_code] => GB\n    [:up_country_code] => GB\n    [:state_province] => NOTTINGHAMSHIRE\n    [:up_state_province] => NOTTINGHAMSHIRE\n    [:individual_flag] => \n    [:up_individual_flag] => \n    [:account_type] => \n    [:up_account_type] => \n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1001473\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-13T10:53:18+01:00\n    [:up_quote_created_time] => 2025-08-13T10:53:18+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3872\n    [:up_end_customer] => 3872\n    [:quote_contact] => 275749\n    [:up_quote_contact] => 275749\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_accounts SET account_csn = :account_csn, email = :email, first_name = :first_name, last_name = :last_name, phone = :phone ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), email = :up_email, first_name = :up_first_name, last_name = :up_last_name, phone = :up_phone;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:account_csn] => ********\n    [:email] => <EMAIL>\n    [:up_email] => <EMAIL>\n    [:first_name] => Luke\n    [:up_first_name] => Luke\n    [:last_name] => Pella\n    [:up_last_name] => Pella\n    [:phone] => +************\n    [:up_phone] => +************\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-28\n    [:up_start_date] => 2025-08-28\n    [:end_date] => 2026-08-27\n    [:up_end_date] => 2026-08-27\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 56699607834629\n    [:up_subscription_id] => 56699607834629\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-27\n    [:up_subscription_endDate] => 2025-08-27\n    [:quote_id] => 2049\n    [:up_quote_id] => 2049\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [additional_recipients] => <json_encode>additionalRecipients</json_encode>\n    [end_customer] => <group_insert_id>end_customer</group_insert_id>\n    [quote_contact] => <group_insert_id>quote_contact</group_insert_id>\n    [agent_account] => <group_insert_id>agent_account</group_insert_id>\n    [admin] => <json_encode>admin</json_encode>\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quotes SET quote_number = :quote_number, quote_status = :quote_status, quote_created_time = :quote_created_time, quote_language = :quote_language, skip_dda_check = :skip_dda_check, quote_currency = :quote_currency, total_list_amount = :total_list_amount, total_net_amount = :total_net_amount, total_amount = :total_amount, total_discount = :total_discount, estimated_tax = :estimated_tax, additional_recipients = :additional_recipients, end_customer = :end_customer, quote_contact = :quote_contact, agent_account = :agent_account ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quote_status = :up_quote_status, quote_created_time = :up_quote_created_time, quote_language = :up_quote_language, skip_dda_check = :up_skip_dda_check, quote_currency = :up_quote_currency, total_list_amount = :up_total_list_amount, total_net_amount = :up_total_net_amount, total_amount = :up_total_amount, total_discount = :up_total_discount, estimated_tax = :up_estimated_tax, additional_recipients = :up_additional_recipients, end_customer = :up_end_customer, quote_contact = :up_quote_contact, agent_account = :up_agent_account;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:quote_number] => Q-1001473\n    [:quote_status] => Draft\n    [:up_quote_status] => Draft\n    [:quote_created_time] => 2025-08-13T10:53:18+01:00\n    [:up_quote_created_time] => 2025-08-13T10:53:18+01:00\n    [:quote_language] => en\n    [:up_quote_language] => en\n    [:skip_dda_check] => \n    [:up_skip_dda_check] => \n    [:quote_currency] => GBP\n    [:up_quote_currency] => GBP\n    [:total_list_amount] => 410\n    [:up_total_list_amount] => 410\n    [:total_net_amount] => 410\n    [:up_total_net_amount] => 410\n    [:total_amount] => 410\n    [:up_total_amount] => 410\n    [:total_discount] => 0\n    [:up_total_discount] => 0\n    [:estimated_tax] => 0\n    [:up_estimated_tax] => 0\n    [:additional_recipients] => []\n    [:up_additional_recipients] => []\n    [:end_customer] => 3872\n    [:up_end_customer] => 3872\n    [:quote_contact] => 275749\n    [:up_quote_contact] => 275749\n    [:agent_account] => 2\n    [:up_agent_account] => 2\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:465] process_extra_fields: Array\n(\n    [quote_id] => <group_insert_id>quote\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:422] SQL Query: INSERT INTO autodesk_quote_line_items SET line_number = :line_number, quantity = :quantity, action = :action, start_date = :start_date, end_date = :end_date, offering_id = :offering_id, offering_code = :offering_code, offering_name = :offering_name, marketing_name = :marketing_name, unit_of_measure = :unit_of_measure, unit_srp = :unit_srp, extended_srp = :extended_srp, discounts_applied = :discounts_applied, end_user_price = :end_user_price, currency = :currency, subscription_id = :subscription_id, subscription_quantity = :subscription_quantity, subscription_endDate = :subscription_endDate, quote_id = :quote_id ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), quantity = :up_quantity, action = :up_action, start_date = :up_start_date, end_date = :up_end_date, offering_id = :up_offering_id, offering_code = :up_offering_code, offering_name = :up_offering_name, marketing_name = :up_marketing_name, unit_of_measure = :up_unit_of_measure, unit_srp = :up_unit_srp, extended_srp = :up_extended_srp, discounts_applied = :up_discounts_applied, end_user_price = :up_end_user_price, currency = :up_currency, subscription_id = :up_subscription_id, subscription_quantity = :up_subscription_quantity, subscription_endDate = :up_subscription_endDate, quote_id = :up_quote_id;
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:423] SQL Params: Array\n(\n    [:line_number] => 1\n    [:quantity] => 1\n    [:up_quantity] => 1\n    [:action] => Renewal\n    [:up_action] => Renewal\n    [:start_date] => 2025-08-28\n    [:up_start_date] => 2025-08-28\n    [:end_date] => 2026-08-27\n    [:up_end_date] => 2026-08-27\n    [:offering_id] => OD-000031\n    [:up_offering_id] => OD-000031\n    [:offering_code] => ACDLT\n    [:up_offering_code] => ACDLT\n    [:offering_name] => AutoCAD LT\n    [:up_offering_name] => AutoCAD LT\n    [:marketing_name] => AutoCAD LT\n    [:up_marketing_name] => AutoCAD LT\n    [:unit_of_measure] => EA\n    [:up_unit_of_measure] => EA\n    [:unit_srp] => 410\n    [:up_unit_srp] => 410\n    [:extended_srp] => 410\n    [:up_extended_srp] => 410\n    [:discounts_applied] => 0\n    [:up_discounts_applied] => 0\n    [:end_user_price] => 410\n    [:up_end_user_price] => 410\n    [:currency] => GBP\n    [:up_currency] => GBP\n    [:subscription_id] => 56699607834629\n    [:up_subscription_id] => 56699607834629\n    [:subscription_quantity] => 1\n    [:up_subscription_quantity] => 1\n    [:subscription_endDate] => 2025-08-27\n    [:up_subscription_endDate] => 2025-08-27\n    [:quote_id] => 2049\n    [:up_quote_id] => 2049\n)\n
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:288]  Skipping array item in ref_subs - no valid data found
[data_importer] [2025-08-13 09:53:24] [data_importer.class.php:259] Skipping bill_plan - no data found at path: billPlans
