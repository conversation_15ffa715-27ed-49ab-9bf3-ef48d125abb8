[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 08:55:15
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:36]  Provided signature: sha256=3908d10d34fb067ac685eaf0e51be4715d2c68414773327d10e332289464d6d7
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:37]  Calculated signature: sha256=19985002215a2d59ae151568c20f6801b3d4340fdd0a91acf33ffb04ad0b130a
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1d7e19cfce9e2c0b\n    [X-B3-<PERSON><PERSON>] => 689c52f196ff1060c251f282bd7b1407\n    [B3] => 689c52f196ff1060c251f282bd7b1407-1d7e19cfce9e2c0b-1\n    [Traceparent] => 00-689c52f196ff1060c251f282bd7b1407-1d7e19cfce9e2c0b-01\n    [X-Amzn-Trace-Id] => Root=1-689c52f1-96ff1060c251f282bd7b1407;Parent=1d7e19cfce9e2c0b;Sampled=1\n    [X-Adsk-Signature] => sha256=3908d10d34fb067ac685eaf0e51be4715d2c68414773327d10e332289464d6d7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2ba03182-d9ee-493d-9af7-f44d6764ea1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"2ba03182-d9ee-493d-9af7-f44d6764ea1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001027","transactionId":"ac02bb63-e033-5b29-a6e6-da75b5181708","quoteStatus":"Draft","message":"Quote# Q-1001027 status changed to Draft.","modifiedAt":"2025-08-13T08:55:12.901Z"},"publishedAt":"2025-08-13T08:55:13.000Z","csn":"5103159758"}
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 08:55:15
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:36]  Provided signature: sha256=19985002215a2d59ae151568c20f6801b3d4340fdd0a91acf33ffb04ad0b130a
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:37]  Calculated signature: sha256=19985002215a2d59ae151568c20f6801b3d4340fdd0a91acf33ffb04ad0b130a
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0e0db9abd67376e4\n    [X-B3-Traceid] => 689c52f196ff1060c251f282bd7b1407\n    [B3] => 689c52f196ff1060c251f282bd7b1407-0e0db9abd67376e4-1\n    [Traceparent] => 00-689c52f196ff1060c251f282bd7b1407-0e0db9abd67376e4-01\n    [X-Amzn-Trace-Id] => Root=1-689c52f1-96ff1060c251f282bd7b1407;Parent=0e0db9abd67376e4;Sampled=1\n    [X-Adsk-Signature] => sha256=19985002215a2d59ae151568c20f6801b3d4340fdd0a91acf33ffb04ad0b130a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2ba03182-d9ee-493d-9af7-f44d6764ea1b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-13 08:55:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"2ba03182-d9ee-493d-9af7-f44d6764ea1b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001027","transactionId":"ac02bb63-e033-5b29-a6e6-da75b5181708","quoteStatus":"Draft","message":"Quote# Q-1001027 status changed to Draft.","modifiedAt":"2025-08-13T08:55:12.901Z"},"publishedAt":"2025-08-13T08:55:13.000Z","csn":"5103159758"}
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 08:58:19
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:36]  Provided signature: sha256=0ddefe2203c28c33e86c8af95194c6cbb5660ae3eb1f1ecce5d9988fd71a855c
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:37]  Calculated signature: sha256=79d7b939dd133ab1ebfc616e40aec1b455970af3bf22dc954fba3f1d5e3b6baa
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6567681fc760d5b5\n    [X-B3-Traceid] => 689c53a8fa0cb190df9a56cd98a0c856\n    [B3] => 689c53a8fa0cb190df9a56cd98a0c856-6567681fc760d5b5-1\n    [Traceparent] => 00-689c53a8fa0cb190df9a56cd98a0c856-6567681fc760d5b5-01\n    [X-Amzn-Trace-Id] => Root=1-689c53a8-fa0cb190df9a56cd98a0c856;Parent=6567681fc760d5b5;Sampled=1\n    [X-Adsk-Signature] => sha256=0ddefe2203c28c33e86c8af95194c6cbb5660ae3eb1f1ecce5d9988fd71a855c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => abb5d950-f3f9-4ffd-9df3-f99ccd679923\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"abb5d950-f3f9-4ffd-9df3-f99ccd679923","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001027","transactionId":"ac02bb63-e033-5b29-a6e6-da75b5181708","quoteStatus":"Quoted","message":"Quote# Q-1001027 status changed to Quoted.","modifiedAt":"2025-08-13T08:58:16.391Z"},"publishedAt":"2025-08-13T08:58:16.000Z","csn":"5103159758"}
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 08:58:19
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:36]  Provided signature: sha256=79d7b939dd133ab1ebfc616e40aec1b455970af3bf22dc954fba3f1d5e3b6baa
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:37]  Calculated signature: sha256=79d7b939dd133ab1ebfc616e40aec1b455970af3bf22dc954fba3f1d5e3b6baa
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e522e9485f8add56\n    [X-B3-Traceid] => 689c53a8fa0cb190df9a56cd98a0c856\n    [B3] => 689c53a8fa0cb190df9a56cd98a0c856-e522e9485f8add56-1\n    [Traceparent] => 00-689c53a8fa0cb190df9a56cd98a0c856-e522e9485f8add56-01\n    [X-Amzn-Trace-Id] => Root=1-689c53a8-fa0cb190df9a56cd98a0c856;Parent=e522e9485f8add56;Sampled=1\n    [X-Adsk-Signature] => sha256=79d7b939dd133ab1ebfc616e40aec1b455970af3bf22dc954fba3f1d5e3b6baa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => abb5d950-f3f9-4ffd-9df3-f99ccd679923\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-13 08:58:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"abb5d950-f3f9-4ffd-9df3-f99ccd679923","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001027","transactionId":"ac02bb63-e033-5b29-a6e6-da75b5181708","quoteStatus":"Quoted","message":"Quote# Q-1001027 status changed to Quoted.","modifiedAt":"2025-08-13T08:58:16.391Z"},"publishedAt":"2025-08-13T08:58:16.000Z","csn":"5103159758"}
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 09:16:25
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:36]  Provided signature: sha256=3cfa9215bd29f1514195fdc383306a65061fd9bc7066050aef7c653b0be5601a
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:37]  Calculated signature: sha256=3cfa9215bd29f1514195fdc383306a65061fd9bc7066050aef7c653b0be5601a
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cb90fe3c3b8333da\n    [X-B3-Traceid] => 689c57e60d48a5762aaead962c7b8a4e\n    [B3] => 689c57e60d48a5762aaead962c7b8a4e-cb90fe3c3b8333da-1\n    [Traceparent] => 00-689c57e60d48a5762aaead962c7b8a4e-cb90fe3c3b8333da-01\n    [X-Amzn-Trace-Id] => Root=1-689c57e6-0d48a5762aaead962c7b8a4e;Parent=cb90fe3c3b8333da;Sampled=1\n    [X-Adsk-Signature] => sha256=3cfa9215bd29f1514195fdc383306a65061fd9bc7066050aef7c653b0be5601a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7917c623-a7f6-4d9f-98fd-2416763d7194\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"7917c623-a7f6-4d9f-98fd-2416763d7194","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001151","transactionId":"04cce22a-9bb5-5a6e-95ca-833cc1af26ac","quoteStatus":"Draft","message":"Quote# Q-1001151 status changed to Draft.","modifiedAt":"2025-08-13T09:16:22.634Z"},"publishedAt":"2025-08-13T09:16:22.000Z","csn":"5103159758"}
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 09:16:25
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:36]  Provided signature: sha256=160472a8c1daad4682007c35512cce635b3019859dd8c12432fcfab9425be510
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:37]  Calculated signature: sha256=3cfa9215bd29f1514195fdc383306a65061fd9bc7066050aef7c653b0be5601a
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3160d29e54a54e49\n    [X-B3-Traceid] => 689c57e60d48a5762aaead962c7b8a4e\n    [B3] => 689c57e60d48a5762aaead962c7b8a4e-3160d29e54a54e49-1\n    [Traceparent] => 00-689c57e60d48a5762aaead962c7b8a4e-3160d29e54a54e49-01\n    [X-Amzn-Trace-Id] => Root=1-689c57e6-0d48a5762aaead962c7b8a4e;Parent=3160d29e54a54e49;Sampled=1\n    [X-Adsk-Signature] => sha256=160472a8c1daad4682007c35512cce635b3019859dd8c12432fcfab9425be510\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7917c623-a7f6-4d9f-98fd-2416763d7194\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-13 09:16:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"7917c623-a7f6-4d9f-98fd-2416763d7194","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001151","transactionId":"04cce22a-9bb5-5a6e-95ca-833cc1af26ac","quoteStatus":"Draft","message":"Quote# Q-1001151 status changed to Draft.","modifiedAt":"2025-08-13T09:16:22.634Z"},"publishedAt":"2025-08-13T09:16:22.000Z","csn":"5103159758"}
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 09:53:22
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:36]  Provided signature: sha256=018ae91de2339922c739483b0ff2b15caffd48fb2ce58b9831762796dbb20f5b
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:37]  Calculated signature: sha256=bf87a5f37155d81744dcbed3e82332c128ce1ab00a0a45a5d0bc341804377f22
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6bed78006f83a95a\n    [X-B3-Traceid] => 689c6090458eb2007ea57d1e2e6549f9\n    [B3] => 689c6090458eb2007ea57d1e2e6549f9-6bed78006f83a95a-1\n    [Traceparent] => 00-689c6090458eb2007ea57d1e2e6549f9-6bed78006f83a95a-01\n    [X-Amzn-Trace-Id] => Root=1-689c6090-458eb2007ea57d1e2e6549f9;Parent=6bed78006f83a95a;Sampled=1\n    [X-Adsk-Signature] => sha256=018ae91de2339922c739483b0ff2b15caffd48fb2ce58b9831762796dbb20f5b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d9e8df26-fd65-4282-a464-1587f9f81d44\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"d9e8df26-fd65-4282-a464-1587f9f81d44","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001473","transactionId":"b30e0409-b179-5297-9d01-651fd3b9bf9a","quoteStatus":"Draft","message":"Quote# Q-1001473 status changed to Draft.","modifiedAt":"2025-08-13T09:53:19.939Z"},"publishedAt":"2025-08-13T09:53:20.000Z","csn":"5103159758"}
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 09:53:22
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:36]  Provided signature: sha256=bf87a5f37155d81744dcbed3e82332c128ce1ab00a0a45a5d0bc341804377f22
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:37]  Calculated signature: sha256=bf87a5f37155d81744dcbed3e82332c128ce1ab00a0a45a5d0bc341804377f22
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fc89cc2cbd9c2f75\n    [X-B3-Traceid] => 689c6090458eb2007ea57d1e2e6549f9\n    [B3] => 689c6090458eb2007ea57d1e2e6549f9-fc89cc2cbd9c2f75-1\n    [Traceparent] => 00-689c6090458eb2007ea57d1e2e6549f9-fc89cc2cbd9c2f75-01\n    [X-Amzn-Trace-Id] => Root=1-689c6090-458eb2007ea57d1e2e6549f9;Parent=fc89cc2cbd9c2f75;Sampled=1\n    [X-Adsk-Signature] => sha256=bf87a5f37155d81744dcbed3e82332c128ce1ab00a0a45a5d0bc341804377f22\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d9e8df26-fd65-4282-a464-1587f9f81d44\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-13 09:53:22] [adwsapi_v2.php:57]  Received webhook data: {"id":"d9e8df26-fd65-4282-a464-1587f9f81d44","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001473","transactionId":"b30e0409-b179-5297-9d01-651fd3b9bf9a","quoteStatus":"Draft","message":"Quote# Q-1001473 status changed to Draft.","modifiedAt":"2025-08-13T09:53:19.939Z"},"publishedAt":"2025-08-13T09:53:20.000Z","csn":"5103159758"}
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 09:54:38
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:36]  Provided signature: sha256=550fa32946382662f1dd8d2c7aa52f2642f8a35b0e4b9ea0ad9da74adfd1abc8
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:37]  Calculated signature: sha256=550fa32946382662f1dd8d2c7aa52f2642f8a35b0e4b9ea0ad9da74adfd1abc8
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5b17bdb8b8c6f5f5\n    [X-B3-Traceid] => 689c60dc80ec6b9fbd06746b6b51be9f\n    [B3] => 689c60dc80ec6b9fbd06746b6b51be9f-5b17bdb8b8c6f5f5-1\n    [Traceparent] => 00-689c60dc80ec6b9fbd06746b6b51be9f-5b17bdb8b8c6f5f5-01\n    [X-Amzn-Trace-Id] => Root=1-689c60dc-80ec6b9fbd06746b6b51be9f;Parent=5b17bdb8b8c6f5f5;Sampled=1\n    [X-Adsk-Signature] => sha256=550fa32946382662f1dd8d2c7aa52f2642f8a35b0e4b9ea0ad9da74adfd1abc8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 436673ac-365d-44bf-b051-548c63e73593\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"436673ac-365d-44bf-b051-548c63e73593","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001473","transactionId":"b30e0409-b179-5297-9d01-651fd3b9bf9a","quoteStatus":"Quoted","message":"Quote# Q-1001473 status changed to Quoted.","modifiedAt":"2025-08-13T09:54:35.904Z"},"publishedAt":"2025-08-13T09:54:36.000Z","csn":"5103159758"}
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:20]  Webhook request received at 2025-08-13 09:54:38
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:36]  Provided signature: sha256=66aa247c4df9d5579ca18c5564ba27528d2467217fff940ab9eaafa1cc9b5f12
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:37]  Calculated signature: sha256=550fa32946382662f1dd8d2c7aa52f2642f8a35b0e4b9ea0ad9da74adfd1abc8
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f24a82617d49021e\n    [X-B3-Traceid] => 689c60dc80ec6b9fbd06746b6b51be9f\n    [B3] => 689c60dc80ec6b9fbd06746b6b51be9f-f24a82617d49021e-1\n    [Traceparent] => 00-689c60dc80ec6b9fbd06746b6b51be9f-f24a82617d49021e-01\n    [X-Amzn-Trace-Id] => Root=1-689c60dc-80ec6b9fbd06746b6b51be9f;Parent=f24a82617d49021e;Sampled=1\n    [X-Adsk-Signature] => sha256=66aa247c4df9d5579ca18c5564ba27528d2467217fff940ab9eaafa1cc9b5f12\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 436673ac-365d-44bf-b051-548c63e73593\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-13 09:54:38] [adwsapi_v2.php:57]  Received webhook data: {"id":"436673ac-365d-44bf-b051-548c63e73593","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-1001473","transactionId":"b30e0409-b179-5297-9d01-651fd3b9bf9a","quoteStatus":"Quoted","message":"Quote# Q-1001473 status changed to Quoted.","modifiedAt":"2025-08-13T09:54:35.904Z"},"publishedAt":"2025-08-13T09:54:36.000Z","csn":"5103159758"}
