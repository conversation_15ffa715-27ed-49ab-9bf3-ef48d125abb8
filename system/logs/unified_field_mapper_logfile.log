[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:190] Date processing: 08/05/2025 -> -94 days remaining
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:190] Date processing: 12/05/2025 -> -90 days remaining
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:31:32] [unified_field_mapper.class.php:190] Date processing: 19/05/2025 -> -83 days remaining
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:190] Date processing: 08/05/2025 -> -94 days remaining
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:190] Date processing: 12/05/2025 -> -90 days remaining
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:35:52] [unified_field_mapper.class.php:190] Date processing: 19/05/2025 -> -83 days remaining
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:190] Date processing: 08/05/2025 -> -94 days remaining
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:190] Date processing: 12/05/2025 -> -90 days remaining
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:38:39] [unified_field_mapper.class.php:190] Date processing: 19/05/2025 -> -83 days remaining
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:190] Date processing: 08/05/2025 -> -94 days remaining
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:190] Date processing: 12/05/2025 -> -90 days remaining
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:39:07] [unified_field_mapper.class.php:190] Date processing: 19/05/2025 -> -83 days remaining
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:190] Date processing: 08/05/2025 -> -94 days remaining
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:190] Date processing: 29/05/2025 -> -73 days remaining
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:190] Date processing: 02/05/2025 -> -100 days remaining
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:40:46] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:190] Date processing: 08/05/2025 -> -94 days remaining
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:190] Date processing: 29/05/2025 -> -73 days remaining
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:137] Normalizing entry from autodesk_subscriptions with 35 fields
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:190] Date processing: 09/05/2025 -> -93 days remaining
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:137] Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:150] Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-10 13:41:31] [unified_field_mapper.class.php:190] Date processing: 02/05/2025 -> -100 days remaining
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:173] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:110] Date processing: 05/06/2025 -> -67 days remaining
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:173] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:110] Date processing: 29/05/2026 -> 291 days remaining
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:110] Date processing: 11/02/2026 -> 184 days remaining
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:110] Date processing: 07/10/2025 -> 57 days remaining
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:22] [unified_field_mapper.class.php:110] Date processing: 22/07/2025 -> -20 days remaining
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:173] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:110] Date processing: 05/06/2025 -> -67 days remaining
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:173] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:110] Date processing: 29/05/2026 -> 291 days remaining
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:110] Date processing: 11/02/2026 -> 184 days remaining
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:110] Date processing: 07/10/2025 -> 57 days remaining
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:23:45] [unified_field_mapper.class.php:110] Date processing: 22/07/2025 -> -20 days remaining
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:173] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:110] Date processing: 05/06/2025 -> -67 days remaining
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:173] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:110] Date processing: 29/05/2026 -> 291 days remaining
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:110] Date processing: 11/02/2026 -> 184 days remaining
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:110] Date processing: 07/10/2025 -> 57 days remaining
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:24:36] [unified_field_mapper.class.php:110] Date processing: 22/07/2025 -> -20 days remaining
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:173] Table autobooks_import_sketchup_data subscription analysis: 7 matches, result: YES
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_sketchup_data with 62 fields
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped sold_to_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped vendor_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_address_1 -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_state -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_zip_code -> postal_code (postal_code, end_customer_zip_code)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_customer_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped agreement_program_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped agreement_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped agreement_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped agreement_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped agreement_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped product_family -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_id -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_status -> status (status, subs_status, subscription_status)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_start_date -> start_date (start_date, subs_startDate, agreement_start_date)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_contact_name -> contact_name (contact_name, end_customer_contact_name, subscription_contact_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped subscription_contact_email -> email (email_address, endcust_primary_admin_email, end_customer_contact_email)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:110] Date processing: 05/06/2025 -> -67 days remaining
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:173] Table autobooks_import_bluebeam_data subscription analysis: 4 matches, result: YES
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:56]  Normalizing entry from autobooks_import_bluebeam_data with 15 fields
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped serial_number -> subscription_reference (subscription_reference, subs_subscriptionReferenceNumber, subscription_id)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped name -> company_name (company_name, endcust_name, end_customer_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped product_name -> product_name (product_name, subs_offeringName, agreement_program_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped quantity -> quantity (quantity, subs_quantity, subscription_quantity)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped end_date -> end_date (end_date, subs_endDate, agreement_end_date)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped account_primary_reseller_name -> reseller_name (reseller_name, partner_name, account_primary_reseller_name)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped order_shipping_address -> address (address, end_customer_address_1)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped order_shipping_city -> city (city, end_customer_city)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped order_shipping_state_province -> state (state, end_customer_state)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:70]  Mapped order_shipping_country -> country (country, end_customer_country)
[unified_field_mapper] [2025-08-11 14:27:02] [unified_field_mapper.class.php:110] Date processing: 29/05/2026 -> 291 days remaining
