<?php
namespace api\data_sources;

use system\data_source_manager;
use system\users;
use PDO;
use system\database;
use edge\edge;
use Exception;

/**
 * Data Sources API
 *
 * Provides API endpoints for managing data sources
 */

/**
 * Get all available database tables
 */
function get_tables($params = []) {
    try {
        $tables = data_source_manager::get_available_tables();

        return [
            'success' => true,
            'data' => $tables,
            'count' => count($tables)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get table information and sample data
 */
function get_table_info($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }

        $table_info = data_source_manager::get_table_info($table_name);
        if (!$table_info) {
            throw new Exception('Table not found or inaccessible');
        }

        $sample_data = data_source_manager::get_sample_data($table_name, 5);

        return [
            'success' => true,
            'table_info' => $table_info,
            'sample_data' => $sample_data
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get all configured data sources
 */
function get_data_sources($params = []) {
    try {
        $data_sources = data_source_manager::get_data_sources();

        return [
            'success' => true,
            'data' => $data_sources,
            'count' => count($data_sources)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Create a new data source (API endpoint)
 */
function create_data_source($params) {
    try {
        // Validate required fields
        $required_fields = ['name'];
        foreach ($required_fields as $field) {
            if (empty($params[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Handle multiple table selection formats
        $tables = [];

        // Try selected_tables JSON format first
        if (isset($params['selected_tables']) && !empty($params['selected_tables'])) {
            $decoded = json_decode($params['selected_tables'], true);
            if (is_array($decoded) && !empty($decoded)) {
                $tables = $decoded;
            }
        }

        // Try tables array format (from form submission)
        if (empty($tables) && isset($params['tables']) && is_array($params['tables'])) {
            $tables = array_values(array_filter($params['tables']));
        }

        // Try single table_name (legacy format)
        if (empty($tables) && !empty($params['table_name'])) {
            $tables = [$params['table_name']];
        }

        // Note: Table validation will be done later after processing multi-table merger configuration

        // Process joins from form data
        $joins = [];
        if (isset($params['joins']) && is_array($params['joins'])) {
            foreach ($params['joins'] as $join_data) {
                if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                    !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                    $joins[] = [
                        'type' => $join_data['type'] ?? 'INNER',
                        'left_table' => $join_data['left_table'],
                        'left_column' => $join_data['left_column'],
                        'right_table' => $join_data['right_table'],
                        'right_column' => $join_data['right_column'],
                        'left_alias' => $join_data['left_alias'] ?? '',
                        'right_alias' => $join_data['right_alias'] ?? ''
                    ];
                }
            }
        }

        // Process selected columns - handle both array and JSON formats
        $selected_columns = [];
        $selected_columns_array = [];
        if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
            // Handle array format from form
            $selected_columns_array = $params['selected_columns'];
        } elseif (isset($params['selected_columns_json'])) {
            // Handle JSON format for backward compatibility
            $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
        }

        // Convert array format to table.column format
        foreach ($selected_columns_array as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        // Process filters
        $filters = [];
        if (isset($params['filters']) && is_array($params['filters'])) {
            foreach ($params['filters'] as $filter) {
                if (!empty($filter['column']) && !empty($filter['operator'])) {
                    $filters[] = [
                        'column' => $filter['column'],
                        'operator' => $filter['operator'],
                        'value' => $filter['value'] ?? ''
                    ];
                }
            }
        }

        // Process table aliases
        $table_aliases = [];
        if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
            foreach ($params['table_aliases'] as $table_name => $alias) {
                if (!empty($alias) && in_array($table_name, $tables)) {
                    $table_aliases[$table_name] = trim($alias);
                }
            }
        }

        // Process column aliases
        $column_aliases = [];
        if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
            foreach ($params['column_aliases'] as $column_key => $alias) {
                if (!empty($alias) && strpos($column_key, '.') !== false) {
                    list($table, $column) = explode('.', $column_key, 2);
                    if (in_array($table, $tables)) {
                        $column_aliases[$column_key] = trim($alias);
                    }
                }
            }
        }

        // Process custom columns
        $custom_columns = [];
        if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
            foreach ($params['custom_columns'] as $custom_column) {
                if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                    $custom_columns[] = [
                        'sql' => trim($custom_column['sql']),
                        'alias' => trim($custom_column['alias'])
                    ];
                }
            }
        }

        // Process custom tables
        $custom_tables = [];
        if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
            foreach ($params['custom_tables'] as $custom_table) {
                if (is_array($custom_table) && !empty($custom_table['sql']) && !empty($custom_table['alias'])) {
                    $custom_tables[] = [
                        'alias' => trim($custom_table['alias']),
                        'sql' => trim($custom_table['sql']),
                        'join_type' => trim($custom_table['join_type'] ?? 'LEFT JOIN'),
                        'join_condition' => trim($custom_table['join_condition'] ?? ''),
                        'columns' => trim($custom_table['columns'] ?? ''),
                        'description' => trim($custom_table['description'] ?? '')
                    ];
                }
            }
        }

        // Process sorting
        $sorting = [];
        if (isset($params['sorting']) && is_array($params['sorting'])) {
            foreach ($params['sorting'] as $sort) {
                if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                    $sorting[] = [
                        'column' => trim($sort['column']),
                        'direction' => strtoupper(trim($sort['direction']))
                    ];
                }
            }
        }

        // Process grouping
        $grouping = [];
        if (isset($params['grouping']) && is_array($params['grouping'])) {
            foreach ($params['grouping'] as $group) {
                if (is_array($group) && !empty($group['column'])) {
                    $grouping[] = [
                        'column' => trim($group['column'])
                    ];
                }
            }
        }

        // Process limits
        $limits = [
            'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
            'limit' => '',
            'offset' => ''
        ];
        if ($limits['enabled']) {
            if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
                $limits['limit'] = (int)$params['limits']['limit'];
            }
            if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
                $limits['offset'] = (int)$params['limits']['offset'];
            }
        }

        // Check if this is a multi-table merger data source
        $data_source_type = $params['data_source_type'] ?? 'standard';

        if ($data_source_type === 'multi_table_merger') {
            // Handle multi-table merger configuration
            $table_patterns = [];
            $explicit_tables = [];

            // Try to get table patterns from JSON string first
            if (!empty($params['table_patterns']) && is_string($params['table_patterns'])) {
                $table_patterns = json_decode($params['table_patterns'], true) ?: [];
            }

            // If no patterns from JSON, try individual pattern inputs
            if (empty($table_patterns) && isset($params['table_patterns']) && is_array($params['table_patterns'])) {
                $table_patterns = array_filter($params['table_patterns'], function($pattern) {
                    return !empty(trim($pattern));
                });
                $table_patterns = array_values($table_patterns); // Re-index
            }

            // Try to get explicit tables from JSON string first
            if (!empty($params['explicit_tables']) && is_string($params['explicit_tables'])) {
                $explicit_tables = json_decode($params['explicit_tables'], true) ?: [];
            }

            // If no tables from JSON, try individual table inputs
            if (empty($explicit_tables) && isset($params['explicit_tables']) && is_array($params['explicit_tables'])) {
                $explicit_tables = array_filter($params['explicit_tables'], function($table) {
                    return !empty(trim($table));
                });
                $explicit_tables = array_values($explicit_tables); // Re-index
            }
            $mapping_method = $params['mapping_method'] ?? 'like_for_like';
            $column_mappings = json_decode($params['column_mappings'] ?? '[]', true) ?: [];
            $unified_mappings = json_decode($params['unified_mappings'] ?? '[]', true) ?: [];

            // Resolve actual tables from patterns and explicit selection
            $resolved_tables = [];
            $available_tables = data_source_manager::get_available_tables();

            // Add tables matching wildcard patterns
            foreach ($table_patterns as $pattern) {
                $pattern_regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';
                foreach ($available_tables as $table) {
                    if (preg_match($pattern_regex, $table['name']) && !in_array($table['name'], $resolved_tables)) {
                        $resolved_tables[] = $table['name'];
                    }
                }
            }

            // Add explicit tables
            foreach ($explicit_tables as $table_name) {
                if (!in_array($table_name, $resolved_tables)) {
                    $resolved_tables[] = $table_name;
                }
            }

            if (empty($resolved_tables)) {
                throw new Exception('No tables selected for multi-table merger data source');
            }

            $config = [
                'name' => $params['name'],
                'table_name' => $resolved_tables[0], // Primary table for backward compatibility
                'description' => $params['description'] ?? '',
                'category' => $params['category'] ?? 'other',
                'data_source_type' => 'multi_table_merger',
                'table_patterns' => $table_patterns,
                'explicit_tables' => $explicit_tables,
                'resolved_tables' => $resolved_tables,
                'mapping_method' => $mapping_method,
                'column_mappings' => $column_mappings,
                'unified_mappings' => $unified_mappings,
                'reference_table' => $params['reference_table'] ?? '',
                'tables' => $resolved_tables, // For compatibility with existing code
                'filters' => $filters
            ];
        } else {
            // Standard multi-table data source configuration
            if (empty($tables)) {
                throw new Exception("At least one table must be selected");
            }

            $config = [
                'name' => $params['name'],
                'table_name' => $tables[0], // Primary table for backward compatibility
                'description' => $params['description'] ?? '',
                'category' => $params['category'] ?? 'other',
                'data_source_type' => 'standard',
                'tables' => $tables,
                'joins' => $joins,
                'selected_columns' => $selected_columns,
                'table_aliases' => $table_aliases,
                'column_aliases' => $column_aliases,
                'custom_columns' => $custom_columns,
                'custom_tables' => $custom_tables,
                'sorting' => $sorting,
                'grouping' => $grouping,
                'limits' => $limits,
                'column_mapping' => $params['column_mapping'] ?? [],
                'filters' => $filters
            ];
        }

        $data_source_id = data_source_manager::create_data_source($config);

        // Return success response with redirect for HTMX
        header('HX-Redirect: ' . APP_ROOT . '/data_sources');
        return '<div class="text-center p-4">
                    <div class="text-green-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Success!</h3>
                    <p class="text-sm text-gray-600 mb-4">Data source created successfully</p>
                    <p class="text-xs text-gray-500">Redirecting to data sources...</p>
                </div>';

    } catch (Exception $e) {
        return '<div class="text-center p-4">
                    <div class="text-red-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error</h3>
                    <p class="text-sm text-gray-600 mb-4">' . htmlspecialchars($e->getMessage()) . '</p>
                    <button type="button"
                            onclick="window.location.href=\'' . APP_ROOT . '/data_sources\'"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Back to Data Sources
                    </button>
                </div>';
    }
}

/**
 * Update an existing data source
 */
function update($params) {
    try {
        $data_source_id = $params['id'] ?? 0;
        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Get existing data source
        $existing = data_source_manager::get_data_source($data_source_id);
        if (!$existing) {
            throw new Exception('Data source not found');
        }

        // Validate required fields
        $required_fields = ['name'];
        foreach ($required_fields as $field) {
            if (empty($params[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Handle both single table (legacy) and multi-table formats
        $tables = [];
        if (isset($params['selected_tables'])) {
            $tables = json_decode($params['selected_tables'], true) ?: [];
        } elseif (!empty($params['table_name'])) {
            $tables = [$params['table_name']];
        }

        // Fallback: if selected_tables is empty or malformed, try to get from tables array
        if (empty($tables) && isset($params['tables']) && is_array($params['tables'])) {
            $tables = array_values($params['tables']);
        }

        // Additional fallback: if selected_tables param exists but is malformed (like just "["), use tables array
        if (empty($tables) && isset($params['selected_tables']) && $params['selected_tables'] === '[') {
            if (isset($params['tables']) && is_array($params['tables'])) {
                $tables = array_values($params['tables']);
            }
        }

        // Check if this is a multi-table merger (update function doesn't fully support it yet)
        $data_source_type = $params['data_source_type'] ?? $existing['data_source_type'] ?? 'standard';

        if ($data_source_type !== 'multi_table_merger' && empty($tables)) {
            throw new Exception("At least one table must be selected");
        }

        // Process joins from form data
        $joins = [];
        if (isset($params['joins']) && is_array($params['joins'])) {
            foreach ($params['joins'] as $join_data) {
                if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                    !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                    $joins[] = [
                        'type' => $join_data['type'] ?? 'INNER',
                        'left_table' => $join_data['left_table'],
                        'left_column' => $join_data['left_column'],
                        'right_table' => $join_data['right_table'],
                        'right_column' => $join_data['right_column'],
                        'left_alias' => $join_data['left_alias'] ?? '',
                        'right_alias' => $join_data['right_alias'] ?? ''
                    ];
                }
            }
        }

        // Process selected columns - handle both array and JSON formats
        $selected_columns = [];
        $selected_columns_array = [];
        if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
            // Handle array format from form
            $selected_columns_array = $params['selected_columns'];
        } elseif (isset($params['selected_columns_json'])) {
            // Handle JSON format for backward compatibility
            $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
        }

        // Convert array format to table.column format
        foreach ($selected_columns_array as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        // Process filters
        $filters = [];
        if (isset($params['filters']) && is_array($params['filters'])) {
            foreach ($params['filters'] as $filter) {
                if (!empty($filter['column']) && !empty($filter['operator'])) {
                    $filters[] = [
                        'column' => $filter['column'],
                        'operator' => $filter['operator'],
                        'value' => $filter['value'] ?? ''
                    ];
                }
            }
        }

        // Process table aliases
        $table_aliases = [];
        if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
            foreach ($params['table_aliases'] as $table_name => $alias) {
                if (!empty($alias) && in_array($table_name, $tables)) {
                    $table_aliases[$table_name] = trim($alias);
                }
            }
        }

        // Process column aliases
        $column_aliases = [];
        if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
            foreach ($params['column_aliases'] as $column_key => $alias) {
                if (!empty($alias) && strpos($column_key, '.') !== false) {
                    list($table, $column) = explode('.', $column_key, 2);
                    if (in_array($table, $tables)) {
                        $column_aliases[$column_key] = trim($alias);
                    }
                }
            }
        }

        // Process custom columns
        $custom_columns = [];
        if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
            foreach ($params['custom_columns'] as $custom_column) {
                if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                    $custom_columns[] = [
                        'sql' => trim($custom_column['sql']),
                        'alias' => trim($custom_column['alias'])
                    ];
                }
            }
        }

        // Process custom tables
        $custom_tables = [];
        if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
            foreach ($params['custom_tables'] as $custom_table) {
                if (is_array($custom_table) && !empty($custom_table['sql']) && !empty($custom_table['alias'])) {
                    $custom_tables[] = [
                        'alias' => trim($custom_table['alias']),
                        'sql' => trim($custom_table['sql']),
                        'join_type' => trim($custom_table['join_type'] ?? 'LEFT JOIN'),
                        'join_condition' => trim($custom_table['join_condition'] ?? ''),
                        'columns' => trim($custom_table['columns'] ?? ''),
                        'description' => trim($custom_table['description'] ?? '')
                    ];
                }
            }
        }

        // Process sorting
        $sorting = [];
        if (isset($params['sorting']) && is_array($params['sorting'])) {
            foreach ($params['sorting'] as $sort) {
                if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                    $sorting[] = [
                        'column' => trim($sort['column']),
                        'direction' => strtoupper(trim($sort['direction']))
                    ];
                }
            }
        }

        // Process grouping
        $grouping = [];
        if (isset($params['grouping']) && is_array($params['grouping'])) {
            foreach ($params['grouping'] as $group) {
                if (is_array($group) && !empty($group['column'])) {
                    $grouping[] = [
                        'column' => trim($group['column'])
                    ];
                }
            }
        }

        // Process limits
        $limits = [
            'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
            'limit' => '',
            'offset' => ''
        ];
        if ($limits['enabled']) {
            if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
                $limits['limit'] = (int)$params['limits']['limit'];
            }
            if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
                $limits['offset'] = (int)$params['limits']['offset'];
            }
        }

        // Prepare update data
        $update_data = [
            'name' => $params['name'],
            'description' => $params['description'] ?? '',
            'category' => $params['category'] ?? 'other',
            'table_name' => $tables[0], // Primary table for backward compatibility
            'tables' => json_encode($tables),
            'joins' => json_encode($joins),
            'selected_columns' => json_encode($selected_columns),
            'table_aliases' => json_encode($table_aliases),
            'column_aliases' => json_encode($column_aliases),
            'custom_columns' => json_encode($custom_columns),
            'custom_tables' => json_encode($custom_tables),
            'sorting' => json_encode($sorting),
            'grouping' => json_encode($grouping),
            'limits' => json_encode($limits),
            'filters' => json_encode($filters),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Convert column_mapping to JSON if provided
        if (isset($params['column_mapping']) && is_array($params['column_mapping'])) {
            $update_data['column_mapping'] = json_encode($params['column_mapping']);
        }

        $db = \system\database::table('autobooks_data_sources');
        $db->where('id', $data_source_id)->update($update_data);

        // Return success response with redirect
        header('HX-Redirect: ' . APP_ROOT . '/data_sources');
        return '<div class="text-center p-4">
                    <div class="text-green-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Success!</h3>
                    <p class="text-sm text-gray-600 mb-4">Data source updated successfully</p>
                </div>';

    } catch (Exception $e) {
        return '<div class="text-center p-4">
                    <div class="text-red-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error!</h3>
                    <p class="text-sm text-gray-600 mb-4">' . htmlspecialchars($e->getMessage()) . '</p>
                </div>';
    }
}

/**
 * Delete a data source
 */
function delete($params) {
    try {
        $data_source_id = $params['id'] ?? 0;
        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Check if data source exists
        $existing = data_source_manager::get_data_source($data_source_id);
        if (!$existing) {
            throw new Exception('Data source not found');
        }

        $db = \system\database::table('autobooks_data_sources');
        $db->where('id', $data_source_id)->delete();

        return ''; // Return empty content so it removes the item

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get data from a data source with filtering and pagination
 */
function get_data($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? 0;
        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Prepare criteria
        $criteria = [];
        if (!empty($params['search'])) {
            $criteria['search'] = $params['search'];
        }
        if (!empty($params['limit'])) {
            $criteria['limit'] = (int)$params['limit'];
        }
        if (!empty($params['offset'])) {
            $criteria['offset'] = (int)$params['offset'];
        }
        if (!empty($params['sort_column'])) {
            $criteria['sort_column'] = $params['sort_column'];
            $criteria['sort_direction'] = $params['sort_direction'] ?? 'asc';
        }

        $result = data_source_manager::get_data_source_data($data_source_id, $criteria);

        return $result;

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get preview data from a data source (API endpoint)
 */
function get_preview_data($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? 0;
        $limit = $params['limit'] ?? 5;

        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => $limit]);

        return $result;

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get sample data from a table (for new data source creation)
 */
function sample_data($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        $limit = $params['limit'] ?? 5;

        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }

        $result = data_source_manager::get_sample_data($table_name, $limit);

        return $result;

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test data source connection and configuration
 */
function test_connection($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? 0;

        if (empty($data_source_id)) {
            throw new Exception('Data source ID is required');
        }

        // Get data source
        $data_source = data_source_manager::get_data_source($data_source_id);
        if (!$data_source) {
            throw new Exception('Data source not found');
        }

        // Test by getting a small sample
        $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);

        if ($result['success']) {
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data_source' => $data_source,
                'sample_count' => $result['count']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Connection failed: ' . $result['error']
            ];
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get data source statistics
 */
function get_stats($params) {
    try {
        $data_sources = data_source_manager::get_data_sources();
        $available_tables = data_source_manager::get_available_tables();

        // Count by category
        $categories = [];
        foreach ($data_sources as $source) {
            $category = $source['category'] ?? 'other';
            if (!isset($categories[$category])) {
                $categories[$category] = 0;
            }
            $categories[$category]++;
        }

        // Count by status
        $statuses = [];
        foreach ($data_sources as $source) {
            $status = $source['status'] ?? 'active';
            if (!isset($statuses[$status])) {
                $statuses[$status] = 0;
            }
            $statuses[$status]++;
        }

        return [
            'success' => true,
            'stats' => [
                'total_data_sources' => count($data_sources),
                'total_available_tables' => count($available_tables),
                'categories' => $categories,
                'statuses' => $statuses
            ]
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Show create data source view
 */
function create_view($params) {

    $selected_table = $params['table'] ?? '';

    // Get available tables for the form
    $available_tables = data_source_manager::get_available_tables();

    // Filter operators
    $filter_operators = [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater Than',
        '>=' => 'Greater Than or Equal',
        '<' => 'Less Than',
        '<=' => 'Less Than or Equal',
        'LIKE' => 'Contains',
        'NOT LIKE' => 'Does Not Contain',
        'IN' => 'In List',
        'NOT IN' => 'Not In List',
        'IS NULL' => 'Is Empty',
        'IS NOT NULL' => 'Is Not Empty'
    ];

    // Category options
    $category_options = [
        'users' => 'Users & Accounts',
        'products' => 'Products & Inventory',
        'orders' => 'Orders & Transactions',
        'content' => 'Content & Media',
        'analytics' => 'Analytics & Reports',
        'system' => 'System & Configuration',
        'other' => 'Other'
    ];

    // Create empty data source for new form
    $data_source = [
        'name' => '',
        'table_name' => $selected_table,
        'tables' => $selected_table ? [$selected_table] : [],
        'description' => '',
        'category' => 'other',
        'filters' => [],
        'joins' => [],
        'selected_columns' => []
    ];

    return Edge::render('data-source-builder', [
        'title' => 'Create Data Source',
        'description' => 'Create a new database data source for tables and email campaigns',
        'mode' => 'create',
        'data_source' => $data_source,
        'available_tables' => $available_tables,
        'filter_operators' => $filter_operators,
        'category_options' => $category_options,
        'redirect_url' => APP_ROOT . '/data_sources'
    ]);
}

/**
 * Show edit data source view
 */
function edit_view($params) {

    $id = $params['id'] ?? null;
    if (!$id) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source ID is required'
        ]);
    }

    // Validate that data source exists
    $data_source = data_source_manager::get_data_source($id);
    if (!$data_source) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source not found'
        ]);
    }

    // Get available tables for the form
    $available_tables = data_source_manager::get_available_tables();

    // Filter operators
    $filter_operators = [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater Than',
        '>=' => 'Greater Than or Equal',
        '<' => 'Less Than',
        '<=' => 'Less Than or Equal',
        'LIKE' => 'Contains',
        'NOT LIKE' => 'Does Not Contain',
        'IN' => 'In List',
        'NOT IN' => 'Not In List',
        'IS NULL' => 'Is Empty',
        'IS NOT NULL' => 'Is Not Empty'
    ];

    // Category options
    $category_options = [
        'users' => 'Users & Accounts',
        'products' => 'Products & Inventory',
        'orders' => 'Orders & Transactions',
        'content' => 'Content & Media',
        'analytics' => 'Analytics & Reports',
        'system' => 'System & Configuration',
        'other' => 'Other'
    ];

    return Edge::render('data-source-builder', [
        'title' => 'Edit Data Source',
        'description' => 'Modify database data source configuration',
        'mode' => 'edit',
        'data_source_id' => $id,
        'data_source' => $data_source,
        'available_tables' => $available_tables,
        'filter_operators' => $filter_operators,
        'category_options' => $category_options,
        'redirect_url' => APP_ROOT . '/data_sources'
    ]);
}

/**
 * Show preview data source view
 */
function preview_view($params) {

    $id = $params['id'] ?? null;
    if (!$id) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source ID is required'
        ]);
    }

    // Validate that data source exists
    $data_source = data_source_manager::get_data_source($id);
    if (!$data_source) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source not found'
        ]);
    }
    // Get sample data and table info for the preview
    $sample_data = data_source_manager::get_data_source_data($id, ['limit' => 50]);
    $table_info = data_source_manager::get_table_info($data_source['table_name']);

    return Edge::render('data-source-preview', [
        'title' => 'Data Source Preview',
        'description' => 'Preview data from configured data source',
        'data_source' => $data_source,
        'sample_data' => $sample_data,
        'table_info' => $table_info
    ]);
}

/**
 * Duplicate a data source (API endpoint)
 */
function duplicate($params) {
    try {
        $source_id = $params['id'] ?? 0;
        if (empty($source_id)) {
            throw new Exception('Data source ID is required');
        }

        $new_name = $params['name'] ?? '';
        $new_id = data_source_manager::duplicate_data_source($source_id, $new_name);

        return [
            'success' => true,
            'message' => 'Data source duplicated successfully',
            'new_id' => $new_id
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Show duplicate data source view (pre-populated create form)
 */
function duplicate_view($params) {
    $id = $params['id'] ?? null;
    if (!$id) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source ID is required'
        ]);
    }

    // Get the original data source
    $original_source = data_source_manager::get_data_source($id);
    if (!$original_source) {
        return Edge::render('error-message', [
            'title' => 'Error',
            'message' => 'Data source not found'
        ]);
    }

    // Prepare the data source for duplication (modify name and remove ID)
    $duplicate_source = $original_source;
    unset($duplicate_source['id']);
    $duplicate_source['name'] = 'Copy of ' . $original_source['name'];

    // Ensure tables field is properly formatted as array
    if (isset($duplicate_source['tables']) && is_string($duplicate_source['tables'])) {
        $duplicate_source['tables'] = json_decode($duplicate_source['tables'], true) ?: [];
    }
    if (!isset($duplicate_source['tables']) && !empty($duplicate_source['table_name'])) {
        $duplicate_source['tables'] = [$duplicate_source['table_name']];
    }

    return Edge::render('data-source-builder', [
        'title' => 'Duplicate Data Source',
        'description' => 'Create a duplicate of an existing data source with all configurations copied',
        'mode' => 'create',
        'data_source' => $duplicate_source,
        'redirect_url' => APP_ROOT . '/data_sources',
        'is_duplicate' => true,
        'original_id' => $id
    ]);
}

/**
 * Get table information fragment for HTMX
 */
function table_info_fragment($params) {
    $table_name = $params['table_name'] ?? '';

    if (empty($table_name)) {
        return '<div class="text-gray-500">No table selected</div>';
    }

    try {
        $available_tables = data_source_manager::get_available_tables();
        $table_info = null;

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $table_info = $table;
                break;
            }
        }

        if (!$table_info) {
            return '<div class="text-red-500">Table not found</div>';
        }

        return Edge::render('data-source-table-info', [
            'table_info' => $table_info
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading table information: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get column list fragment for HTMX
 */
function column_list_fragment($params) {
    $table_name = $params['table_name'] ?? '';

    if (empty($table_name)) {
        return '<div class="text-gray-500">No table selected</div>';
    }

    try {
        $available_tables = data_source_manager::get_available_tables();
        $columns = [];

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $columns = $table['columns'];
                break;
            }
        }

        return Edge::render('data-source-column-list', [
            'columns' => $columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get data preview fragment for HTMX
 */
function data_preview_fragment($params) {
    // Parse all the same data as query_preview_fragment
    $tables = [];
    $joins = [];
    $selected_columns = [];
    $filters = [];

    // Parse selected tables
    if (isset($params['selected_tables'])) {
        $tables = json_decode($params['selected_tables'], true) ?: [];
    } elseif (!empty($params['table_name'])) {
        $tables = [$params['table_name']];
    }

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($tables) && isset($params['tables']) && is_array($params['tables'])) {
        $tables = array_values($params['tables']);
    }

    // Parse joins from form data (same logic as query_preview_fragment)
    if (isset($params['joins']) && is_array($params['joins'])) {
        foreach ($params['joins'] as $join_data) {
            if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                // Format column references properly
                $left_column = $join_data['left_column'];
                $right_column = $join_data['right_column'];

                // If columns don't already have table prefix, add it
                if (strpos($left_column, '.') === false) {
                    $left_column = $join_data['left_table'] . '.' . $left_column;
                }
                if (strpos($right_column, '.') === false) {
                    $right_column = $join_data['right_table'] . '.' . $right_column;
                }

                $joins[] = [
                    'type' => $join_data['type'] ?? 'INNER',
                    'table' => $join_data['right_table'],
                    'left_column' => $left_column,
                    'right_column' => $right_column,
                    'left_table' => $join_data['left_table'],
                    'right_table' => $join_data['right_table'],
                    'left_alias' => $join_data['left_alias'] ?? '',
                    'right_alias' => $join_data['right_alias'] ?? ''
                ];
            }
        }
    }

    // Parse selected columns - handle both array and JSON formats
    $selected_columns_array = [];
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        // Handle array format from form
        $selected_columns_array = $params['selected_columns'];
    } elseif (isset($params['selected_columns_json'])) {
        // Handle JSON format for backward compatibility
        $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];
    }

    // Convert array format to table.column format
    foreach ($selected_columns_array as $column) {
        if (strpos($column, '.') !== false) {
            list($table, $col) = explode('.', $column, 2);
            $selected_columns[$table][] = $col;
        }
    }

    // If no columns are selected, auto-select a few columns from each table to show the alias format
    if (empty($selected_columns) && !empty($tables)) {
        // First, identify which tables have aliases in joins
        $tables_with_aliases = [];
        if (!empty($joins)) {
            foreach ($joins as $join) {
                if (!empty($join['right_alias']) && !empty($join['right_table'])) {
                    $tables_with_aliases[$join['right_table']] = true;
                }
                if (!empty($join['left_alias']) && !empty($join['left_table'])) {
                    $tables_with_aliases[$join['left_table']] = true;
                }
            }
        }

        foreach ($tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info && !empty($table_info['columns'])) {

                // If this table has aliases in joins, DON'T create base table selections
                if (isset($tables_with_aliases[$table_name])) {
                    continue; // Skip base table, will be handled by alias logic below
                }

                // Select first few columns from each table for preview (only for tables without aliases)
                $column_count = 0;
                foreach ($table_info['columns'] as $column) {
                    if ($column_count >= 3) break; // Limit to 3 columns per table for preview
                    $selected_columns[$table_name][] = $column['Field'];
                    $column_count++;
                }
            }
        }

        // Handle aliases - create selections for each alias
        if (!empty($joins)) {
            foreach ($joins as $join) {
                if (!empty($join['right_alias']) && !empty($join['right_table'])) {
                    $alias = $join['right_alias'];
                    $table_name = $join['right_table'];

                    if (!isset($selected_columns[$alias])) {
                        $table_info = data_source_manager::get_table_info($table_name);
                        if ($table_info && !empty($table_info['columns'])) {
                            $column_count = 0;
                            foreach ($table_info['columns'] as $column) {
                                if ($column_count >= 3) break; // Limit to 3 columns per alias for preview
                                $selected_columns[$alias][] = $column['Field'];
                                $column_count++;
                            }
                        }
                    }
                }

                // Also handle left aliases (like the main table alias)
                if (!empty($join['left_alias']) && !empty($join['left_table'])) {
                    $alias = $join['left_alias'];
                    $table_name = $join['left_table'];

                    if (!isset($selected_columns[$alias])) {
                        $table_info = data_source_manager::get_table_info($table_name);
                        if ($table_info && !empty($table_info['columns'])) {
                            $column_count = 0;
                            foreach ($table_info['columns'] as $column) {
                                if ($column_count >= 3) break; // Limit to 3 columns per alias for preview
                                $selected_columns[$alias][] = $column['Field'];
                                $column_count++;
                            }
                        }
                    }
                }
            }
        }
    }

    // Parse filters from form data
    if (isset($params['filters']) && is_array($params['filters'])) {
        foreach ($params['filters'] as $filter) {
            if (!empty($filter['column']) && !empty($filter['operator'])) {
                $filters[] = [
                    'column' => $filter['column'],
                    'operator' => $filter['operator'],
                    'value' => $filter['value'] ?? ''
                ];
            }
        }
    }

    // Parse table aliases from form data
    $table_aliases = [];
    if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
        foreach ($params['table_aliases'] as $table_name => $alias) {
            if (!empty($alias) && in_array($table_name, $tables)) {
                $table_aliases[$table_name] = trim($alias);
            }
        }
    }

    // Parse column aliases from form data
    $column_aliases = [];
    if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
        foreach ($params['column_aliases'] as $column_key => $alias) {
            if (!empty($alias) && strpos($column_key, '.') !== false) {
                list($table, $column) = explode('.', $column_key, 2);
                if (in_array($table, $tables)) {
                    $column_aliases[$column_key] = trim($alias);
                }
            }
        }
    }

    // Parse custom columns from form data
    $custom_columns = [];
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                $custom_columns[] = [
                    'sql' => trim($custom_column['sql']),
                    'alias' => trim($custom_column['alias'])
                ];
            }
        }
    }

    // Parse sorting from form data
    $sorting = [];
    if (isset($params['sorting']) && is_array($params['sorting'])) {
        foreach ($params['sorting'] as $sort) {
            if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                $sorting[] = [
                    'column' => trim($sort['column']),
                    'direction' => strtoupper(trim($sort['direction']))
                ];
            }
        }
    }

    // Parse grouping from form data
    $grouping = [];
    if (isset($params['grouping']) && is_array($params['grouping'])) {
        foreach ($params['grouping'] as $group) {
            if (is_array($group) && !empty($group['column'])) {
                $grouping[] = [
                    'column' => trim($group['column'])
                ];
            }
        }
    }

    // Parse limits from form data
    $limits = [
        'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
        'limit' => '',
        'offset' => ''
    ];
    if ($limits['enabled']) {
        if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
            $limits['limit'] = (int)$params['limits']['limit'];
        }
        if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
            $limits['offset'] = (int)$params['limits']['offset'];
        }
    }

    // Parse custom tables from form data
    $custom_tables = [];
    if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
        foreach ($params['custom_tables'] as $custom_table) {
            if (is_array($custom_table) && !empty($custom_table['sql']) && !empty($custom_table['alias'])) {
                $custom_tables[] = [
                    'alias' => trim($custom_table['alias']),
                    'sql' => trim($custom_table['sql']),
                    'join_type' => trim($custom_table['join_type'] ?? 'LEFT JOIN'),
                    'join_condition' => trim($custom_table['join_condition'] ?? ''),
                    'columns' => trim($custom_table['columns'] ?? ''),
                    'description' => trim($custom_table['description'] ?? '')
                ];
            }
        }
    }

    if (empty($tables)) {
        return '<div class="text-gray-500">No tables selected</div>';
    }

    try {
        // Debug: Log what data we're working with
        if (function_exists('tcs_log')) {
            tcs_log([
                'function' => 'data_preview_fragment',
                'tables' => $tables,
                'joins' => $joins,
                'selected_columns' => $selected_columns,
                'table_aliases' => $table_aliases
            ], 'preview_debug');
        }

        // Build the same query as the preview
        $query = build_multi_table_query($tables, $joins, $selected_columns, $filters, $table_aliases, $column_aliases, $custom_columns, $sorting, $grouping, $limits, $custom_tables);

        // Add LIMIT for preview
        $query .= " LIMIT 10";

        // Debug: Log the generated query
        if (function_exists('tcs_log')) {
            tcs_log([
                'function' => 'data_preview_fragment',
                'generated_query' => $query
            ], 'preview_debug');
        }

        // Execute the query directly
        $result = execute_preview_query($query);

        if (!$result['success']) {
            // Return error with HX-Reswap header to prevent replacement
            if (function_exists('header')) {
                header('HX-Reswap: none');
            }
            return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                        <strong>Data Preview Error:</strong> ' . htmlspecialchars($result['error'] ?? 'Unknown error') . '
                    </div>';
        }

        return Edge::render('data-source-preview-table', [
            'data' => $result['data'] ?? [],
            'columns' => !empty($result['data']) ? array_keys($result['data'][0]) : []
        ]);

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Data Preview Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Get filter row fragment for HTMX
 */
function filter_row_fragment($params) {
    $table_name = $params['table_name'] ?? '';
    $filter_index = $params['filter_index'] ?? 0;

    try {
        $available_tables = data_source_manager::get_available_tables();
        $columns = [];

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $columns = $table['columns'];
                break;
            }
        }

        // Filter operators
        $filter_operators = [
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'LIKE' => 'Contains',
            'NOT LIKE' => 'Does Not Contain',
            'IN' => 'In List',
            'NOT IN' => 'Not In List',
            'IS NULL' => 'Is Empty',
            'IS NOT NULL' => 'Is Not Empty'
        ];

        return Edge::render('data-source-filter-row', [
            'columns' => $columns,
            'filter_operators' => $filter_operators,
            'filter_index' => $filter_index
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error creating filter: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove filter row (returns empty content for HTMX)
 */
function remove_filter_row($params) {
    // Simply return empty content to remove the filter row
    return '';
}

/**
 * Test table connection for form builder
 */
function test_table_connection($params) {
    // Get table name from either old single table format or new multi-table format
    $table_name = '';

    if (!empty($params['table_name'])) {
        $table_name = $params['table_name'];
    } elseif (!empty($params['selected_tables'])) {
        $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        $table_name = $selected_tables[0] ?? '';
    }

    if (empty($table_name)) {
        return '<div class="bg-red-50 border border-red-200 rounded-md p-4"><div class="text-red-800">Please select a table first</div></div>';
    }

    try {
        $data = sample_data([
            'table_name' => $table_name,
            'limit' => 1
        ]);

        if ($data['success']) {
            return '<div class="bg-green-50 border border-green-200 rounded-md p-4"><div class="text-green-800">✓ Connection successful! Table is accessible and contains data.</div></div>';
        } else {
            return '<div class="bg-red-50 border border-red-200 rounded-md p-4"><div class="text-red-800">✗ Connection failed: ' . htmlspecialchars($data['error'] ?? 'Unknown error') . '</div></div>';
        }

    } catch (Exception $e) {
        return '<div class="bg-red-50 border border-red-200 rounded-md p-4"><div class="text-red-800">✗ Connection test failed: ' . htmlspecialchars($e->getMessage()) . '</div></div>';
    }
}

/**
 * Generate and display the final SQL query
 */
function query_preview_fragment($params) {
    // Debug logging
    tcs_log("query_preview_fragment - selected_tables param: " . ($params['selected_tables'] ?? 'null'), 'data_source_debug');
    tcs_log("query_preview_fragment - tables param: " . json_encode($params['tables'] ?? []), 'data_source_debug');
    tcs_log("query_preview_fragment - selected_columns_json param: " . ($params['selected_columns_json'] ?? 'null'), 'data_source_debug');
    tcs_log("query_preview_fragment - selected_columns param: " . json_encode($params['selected_columns'] ?? []), 'data_source_debug');

    // Handle both single table (legacy) and multi-table formats
    $tables = [];
    $joins = [];
    $selected_columns = [];
    $filters = [];

    // Parse selected tables
    if (isset($params['selected_tables'])) {
        $tables = json_decode($params['selected_tables'], true) ?: [];
    } elseif (!empty($params['table_name'])) {
        $tables = [$params['table_name']];
    }

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($tables) && isset($params['tables']) && is_array($params['tables'])) {
        $tables = array_values($params['tables']);
    }

    // Additional fallback: if selected_tables param exists but is malformed (like just "["), use tables array
    if (empty($tables) && isset($params['selected_tables']) && $params['selected_tables'] === '[') {
        if (isset($params['tables']) && is_array($params['tables'])) {
            $tables = array_values($params['tables']);
        }
    }

    // Parse joins from form data
    if (isset($params['joins']) && is_array($params['joins'])) {
        foreach ($params['joins'] as $join_data) {
            if (is_array($join_data) && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                // Format column references properly
                $left_column = $join_data['left_column'];
                $right_column = $join_data['right_column'];

                // If columns don't already have table prefix, add it
                if (strpos($left_column, '.') === false) {
                    $left_column = $join_data['left_table'] . '.' . $left_column;
                }
                if (strpos($right_column, '.') === false) {
                    $right_column = $join_data['right_table'] . '.' . $right_column;
                }

                $joins[] = [
                    'type' => $join_data['type'] ?? 'INNER',
                    'table' => $join_data['right_table'],
                    'left_column' => $left_column,
                    'right_column' => $right_column,
                    'left_table' => $join_data['left_table'],
                    'right_table' => $join_data['right_table'],
                    'left_alias' => $join_data['left_alias'] ?? '',
                    'right_alias' => $join_data['right_alias'] ?? ''
                ];
            }
        }
    } elseif (isset($params['joins']) && is_string($params['joins'])) {
        // Handle JSON string format (for backward compatibility)
        $joins = json_decode($params['joins'], true) ?: [];
    }

    // Parse selected columns - handle both array and JSON formats
    $selected_columns_array = [];
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        // Handle array format from form
        $selected_columns_array = $params['selected_columns'];
    } elseif (isset($params['selected_columns_json'])) {
        // Handle JSON format for backward compatibility
        $selected_columns_array = json_decode($params['selected_columns_json'], true) ?: [];

        // Fallback: if selected_columns_json is malformed (like just "["), use selected_columns array
        if (empty($selected_columns_array) && isset($params['selected_columns_json']) && $params['selected_columns_json'] === '[') {
            if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
                $selected_columns_array = $params['selected_columns'];
            }
        }
    }

    // Convert array format to table.column format
    foreach ($selected_columns_array as $column) {
        if (strpos($column, '.') !== false) {
            list($table, $col) = explode('.', $column, 2);
            $selected_columns[$table][] = $col;
        }
    }

    // Parse filters from form data
    if (isset($params['filters']) && is_array($params['filters'])) {
        foreach ($params['filters'] as $filter) {
            if (!empty($filter['column']) && !empty($filter['operator'])) {
                $filters[] = [
                    'column' => $filter['column'],
                    'operator' => $filter['operator'],
                    'value' => $filter['value'] ?? ''
                ];
            }
        }
    }

    // Parse table aliases from form data
    $table_aliases = [];
    if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
        foreach ($params['table_aliases'] as $table_name => $alias) {
            if (!empty($alias) && in_array($table_name, $tables)) {
                $table_aliases[$table_name] = trim($alias);
            }
        }
    }

    // Parse column aliases from form data
    $column_aliases = [];
    if (isset($params['column_aliases']) && is_array($params['column_aliases'])) {
        foreach ($params['column_aliases'] as $column_key => $alias) {
            if (!empty($alias) && strpos($column_key, '.') !== false) {
                list($table, $column) = explode('.', $column_key, 2);
                if (in_array($table, $tables)) {
                    $column_aliases[$column_key] = trim($alias);
                }
            }
        }
    }

    // Parse custom columns from form data
    $custom_columns = [];
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (is_array($custom_column) && !empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                $custom_columns[] = [
                    'sql' => trim($custom_column['sql']),
                    'alias' => trim($custom_column['alias'])
                ];
            }
        }
    }

    // Parse sorting from form data
    $sorting = [];
    if (isset($params['sorting']) && is_array($params['sorting'])) {
        foreach ($params['sorting'] as $sort) {
            if (is_array($sort) && !empty($sort['column']) && !empty($sort['direction'])) {
                $sorting[] = [
                    'column' => trim($sort['column']),
                    'direction' => strtoupper(trim($sort['direction']))
                ];
            }
        }
    }

    // Parse grouping from form data
    $grouping = [];
    if (isset($params['grouping']) && is_array($params['grouping'])) {
        foreach ($params['grouping'] as $group) {
            if (is_array($group) && !empty($group['column'])) {
                $grouping[] = [
                    'column' => trim($group['column'])
                ];
            }
        }
    }

    // Parse limits from form data
    $limits = [
        'enabled' => isset($params['limits']['enabled']) && $params['limits']['enabled'] == '1',
        'limit' => '',
        'offset' => ''
    ];
    if ($limits['enabled']) {
        if (isset($params['limits']['limit']) && is_numeric($params['limits']['limit'])) {
            $limits['limit'] = (int)$params['limits']['limit'];
        }
        if (isset($params['limits']['offset']) && is_numeric($params['limits']['offset'])) {
            $limits['offset'] = (int)$params['limits']['offset'];
        }
    }

    // Parse custom tables from form data
    $custom_tables = [];
    if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
        foreach ($params['custom_tables'] as $custom_table) {
            if (is_array($custom_table) && !empty($custom_table['sql']) && !empty($custom_table['alias'])) {
                $custom_tables[] = [
                    'alias' => trim($custom_table['alias']),
                    'sql' => trim($custom_table['sql']),
                    'join_type' => trim($custom_table['join_type'] ?? 'LEFT JOIN'),
                    'join_condition' => trim($custom_table['join_condition'] ?? ''),
                    'columns' => trim($custom_table['columns'] ?? ''),
                    'description' => trim($custom_table['description'] ?? '')
                ];
            }
        }
    }

    // Debug logging of final parsed values
    tcs_log("query_preview_fragment - final tables: " . json_encode($tables), 'data_source_debug');
    tcs_log("query_preview_fragment - final joins: " . json_encode($joins), 'data_source_debug');
    tcs_log("query_preview_fragment - final selected_columns: " . json_encode($selected_columns), 'data_source_debug');
    tcs_log("query_preview_fragment - final filters: " . json_encode($filters), 'data_source_debug');

    if (empty($tables)) {
        return '<div class="text-gray-500">Select tables to see the query preview</div>';
    }

    try {
        $query = build_multi_table_query($tables, $joins, $selected_columns, $filters, $table_aliases, $column_aliases, $custom_columns, $sorting, $grouping, $limits, $custom_tables);

        return Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $tables),
            'filters' => $filters,
            'tables' => $tables,
            'joins' => $joins,
            'selected_columns' => $selected_columns
        ]);

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Query Preview Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Build SQL query from table name and filters
 */
function build_query_from_filters($table_name, $filters = []) {
    $query = "SELECT * FROM `{$table_name}`";

    if (!empty($filters)) {
        $where_conditions = [];

        foreach ($filters as $filter) {
            $column = $filter['column'];
            $operator = $filter['operator'];
            $value = $filter['value'];

            switch ($operator) {
                case 'IS NULL':
                    $where_conditions[] = "`{$column}` IS NULL";
                    break;
                case 'IS NOT NULL':
                    $where_conditions[] = "`{$column}` IS NOT NULL";
                    break;
                case 'IN':
                    $values = array_map('trim', explode(',', $value));
                    $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
                    $where_conditions[] = "`{$column}` IN (" . implode(', ', $quoted_values) . ")";
                    break;
                case 'NOT IN':
                    $values = array_map('trim', explode(',', $value));
                    $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
                    $where_conditions[] = "`{$column}` NOT IN (" . implode(', ', $quoted_values) . ")";
                    break;
                case 'LIKE':
                    $where_conditions[] = "`{$column}` LIKE '%" . addslashes($value) . "%'";
                    break;
                case 'NOT LIKE':
                    $where_conditions[] = "`{$column}` NOT LIKE '%" . addslashes($value) . "%'";
                    break;
                default:
                    $where_conditions[] = "`{$column}` {$operator} '" . addslashes($value) . "'";
                    break;
            }
        }

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(' AND ', $where_conditions);
        }
    }

    return $query;
}

/**
 * Get table selection fragment for multi-table support
 */
function table_selection_fragment($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $available_tables = data_source_manager::get_available_tables();

    return Edge::render('data-source-table-selection', [
        'available_tables' => $available_tables,
        'selected_tables' => $selected_tables
    ]);
}

/**
 * Add a new table to the data source
 */
function add_table_fragment($params) {
    $table_name = $params['table_name'] ?? '';
    $table_index = $params['table_index'] ?? 0;

    if (empty($table_name)) {
        return '<div class="text-red-500">Please select a table</div>';
    }

    try {
        $available_tables = data_source_manager::get_available_tables();
        $table_info = null;

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $table_info = $table;
                break;
            }
        }

        if (!$table_info) {
            return '<div class="text-red-500">Table not found</div>';
        }

        return Edge::render('data-source-table-item', [
            'table_info' => $table_info,
            'table_index' => $table_index,
            'is_primary' => $table_index == 0
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding table: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get join configuration fragment
 */
function join_configuration_fragment($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    $existing_joins = json_decode($params['joins'] ?? '[]', true) ?: [];

    if (count($selected_tables) < 2) {
        return '<div class="text-gray-500">Add at least 2 tables to configure joins</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        return Edge::render('data-source-join-configuration', [
            'selected_tables' => $selected_tables,
            'table_columns' => $table_columns,
            'existing_joins' => $existing_joins
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error loading join configuration: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get column selection fragment for multi-table
 */
function column_selection_fragment($params) {
    // Parse selected_tables parameter
    $selected_tables = [];
    if (isset($params['selected_tables'])) {
        if (is_array($params['selected_tables'])) {
            $selected_tables = $params['selected_tables'];
        } elseif (is_string($params['selected_tables']) && !empty($params['selected_tables'])) {
            $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        }
    }

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    // Parse selected_columns parameter
    $selected_columns = [];
    if (isset($params['selected_columns'])) {
        if (is_array($params['selected_columns'])) {
            $selected_columns = $params['selected_columns'];
        } elseif (is_string($params['selected_columns']) && !empty($params['selected_columns'])) {
            $selected_columns = json_decode($params['selected_columns'], true) ?: [];
        }
    }

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    // Parse joins to get table aliases
    $joins = [];
    if (isset($params['joins'])) {
        if (is_array($params['joins'])) {
            $joins = $params['joins'];
        } elseif (is_string($params['joins']) && !empty($params['joins'])) {
            $joins = json_decode($params['joins'], true) ?: [];
        }
    }

    // Build a map of table aliases from joins
    $table_aliases_map = [];
    foreach ($joins as $join) {
        if (!empty($join['left_alias']) && !empty($join['left_table'])) {
            $table_aliases_map[$join['left_table']][] = $join['left_alias'];
        }
        if (!empty($join['right_alias']) && !empty($join['right_table'])) {
            $table_aliases_map[$join['right_table']][] = $join['right_alias'];
        }
    }

    // Also include table aliases from the table_aliases parameter
    $table_aliases = [];
    if (isset($params['table_aliases']) && is_array($params['table_aliases'])) {
        foreach ($params['table_aliases'] as $table_name => $alias) {
            if (!empty($alias) && in_array($table_name, $selected_tables)) {
                $table_aliases_map[$table_name][] = $alias;
            }
        }
    }

    try {
        // Get column information for all selected tables, including aliases
        $table_columns = [];
        $table_display_info = []; // Maps display key to table info for the template
        $auto_selected_columns = []; // Track columns that should be auto-selected

        // First, determine which tables are used only as aliases (shouldn't appear as base tables)
        $tables_with_aliases = [];
        foreach ($table_aliases_map as $table_name => $aliases) {
            if (!empty($aliases)) {
                $tables_with_aliases[] = $table_name;
            }
        }

        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if (!$table_info) {
                continue;
            }

            // Check if this table has aliases
            if (isset($table_aliases_map[$table_name]) && !empty($table_aliases_map[$table_name])) {
                // Create separate entries for each alias - DON'T create entry for base table
                foreach ($table_aliases_map[$table_name] as $alias) {
                    $display_key = $alias . ' (' . $table_name . ')';
                    $table_columns[$display_key] = $table_info['columns'];
                    $table_display_info[$display_key] = [
                        'table_name' => $table_name,
                        'alias' => $alias,
                        'display_name' => $display_key
                    ];

                    // Auto-select first few columns for this alias if no columns are currently selected
                    if (empty($selected_columns)) {
                        $column_count = 0;
                        foreach ($table_info['columns'] as $column) {
                            if ($column_count >= 3) break; // Limit to 3 columns per alias for preview
                            $auto_selected_columns[] = $alias . '.' . $column['Field'];
                            $column_count++;
                        }
                    }
                }
            } else {
                // Only create entry for table name if it's not used exclusively as aliases
                // This prevents creating entries for tables that only appear as aliases in joins
                $table_used_only_as_aliases = false;

                // Check if this table appears in any joins only as aliases
                foreach ($joins as $join) {
                    if (($join['left_table'] === $table_name && !empty($join['left_alias'])) ||
                        ($join['right_table'] === $table_name && !empty($join['right_alias']))) {
                        // This table is used with an alias in joins
                        // Check if it's ONLY used with aliases (never without)
                        $used_without_alias = false;
                        foreach ($joins as $check_join) {
                            if (($check_join['left_table'] === $table_name && empty($check_join['left_alias'])) ||
                                ($check_join['right_table'] === $table_name && empty($check_join['right_alias']))) {
                                $used_without_alias = true;
                                break;
                            }
                        }
                        if (!$used_without_alias) {
                            $table_used_only_as_aliases = true;
                            break;
                        }
                    }
                }

                // Only create table entry if it's not used exclusively as aliases
                if (!$table_used_only_as_aliases) {
                    $table_columns[$table_name] = $table_info['columns'];
                    $table_display_info[$table_name] = [
                        'table_name' => $table_name,
                        'alias' => null,
                        'display_name' => $table_name
                    ];

                    // Auto-select first few columns for this table if no columns are currently selected
                    if (empty($selected_columns)) {
                        $column_count = 0;
                        foreach ($table_info['columns'] as $column) {
                            if ($column_count >= 3) break; // Limit to 3 columns per table for preview
                            $auto_selected_columns[] = $table_name . '.' . $column['Field'];
                            $column_count++;
                        }
                    }
                }
            }
        }

        // Add custom tables to the column selection
        if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
            foreach ($params['custom_tables'] as $custom_table) {
                if (is_array($custom_table) && !empty($custom_table['alias']) && !empty($custom_table['sql'])) {
                    $custom_columns = [];

                    // Try to get columns from the columns field first
                    if (!empty($custom_table['columns'])) {
                        $custom_columns = array_map('trim', explode(',', $custom_table['columns']));
                    } else {
                        // If columns field is empty, try to extract from SQL
                        $custom_columns = extract_columns_from_sql($custom_table['sql']);
                    }

                    // Create column info structure similar to database columns
                    $column_info = [];
                    foreach ($custom_columns as $column_name) {
                        if (!empty($column_name)) {
                            $column_info[] = [
                                'Field' => $column_name,
                                'Type' => 'varchar(255)', // Default type for custom table columns
                                'Null' => 'YES',
                                'Key' => '',
                                'Default' => null,
                                'Extra' => ''
                            ];
                        }
                    }

                    if (!empty($column_info)) {
                        $display_key = $custom_table['alias'] . ' (custom)';
                        $table_columns[$display_key] = $column_info;
                        $table_display_info[$display_key] = [
                            'table_name' => $custom_table['alias'],
                            'alias' => $custom_table['alias'],
                            'display_name' => $display_key,
                            'is_custom' => true
                        ];

                        // Auto-select first few columns from custom table if no columns are currently selected
                        if (empty($selected_columns)) {
                            $column_count = 0;
                            foreach ($column_info as $column) {
                                if ($column_count >= 3) break; // Limit to 3 columns per custom table for preview
                                $auto_selected_columns[] = $custom_table['alias'] . '.' . $column['Field'];
                                $column_count++;
                            }
                        }
                    }
                }
            }
        }

        // If no columns were selected, use the auto-selected ones
        if (empty($selected_columns) && !empty($auto_selected_columns)) {
            $selected_columns = $auto_selected_columns;
        }

        return Edge::render('data-source-column-selection', [
            'table_columns' => $table_columns,
            'table_display_info' => $table_display_info,
            'selected_columns' => $selected_columns,
            'column_aliases' => []
        ]);

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Column Selection Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Extract column names from a SQL SELECT statement
 */
function extract_columns_from_sql($sql) {
    $columns = [];

    try {
        // Remove comments and normalize whitespace
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        $sql = preg_replace('/\s+/', ' ', trim($sql));

        // Find the SELECT clause
        if (preg_match('/SELECT\s+(.*?)\s+FROM/i', $sql, $matches)) {
            $select_clause = $matches[1];

            // Split by comma, but be careful of commas inside functions
            $parts = [];
            $current_part = '';
            $paren_count = 0;

            for ($i = 0; $i < strlen($select_clause); $i++) {
                $char = $select_clause[$i];

                if ($char === '(') {
                    $paren_count++;
                } elseif ($char === ')') {
                    $paren_count--;
                } elseif ($char === ',' && $paren_count === 0) {
                    $parts[] = trim($current_part);
                    $current_part = '';
                    continue;
                }

                $current_part .= $char;
            }

            if (!empty($current_part)) {
                $parts[] = trim($current_part);
            }

            // Extract column names/aliases from each part
            foreach ($parts as $part) {
                $part = trim($part);

                // Check for AS alias
                if (preg_match('/\s+AS\s+(.+)$/i', $part, $alias_matches)) {
                    $alias = trim($alias_matches[1], '`"\'');
                    $columns[] = $alias;
                } elseif (preg_match('/\s+([a-zA-Z_][a-zA-Z0-9_]*)$/', $part, $alias_matches)) {
                    // Check for implicit alias (space-separated)
                    $alias = trim($alias_matches[1], '`"\'');
                    $columns[] = $alias;
                } else {
                    // No alias, try to extract column name
                    if (preg_match('/([a-zA-Z_][a-zA-Z0-9_]*)\s*$/', $part, $col_matches)) {
                        $column = trim($col_matches[1], '`"\'');
                        $columns[] = $column;
                    }
                }
            }
        }
    } catch (Exception $e) {
        // If parsing fails, return empty array
        tcs_log("Error extracting columns from SQL: " . $e->getMessage(), 'sql_parser');
    }

    return array_unique(array_filter($columns));
}

/**
 * Build complex SQL query with joins
 */
function build_multi_table_query($tables, $joins = [], $selected_columns = [], $filters = [], $table_aliases = [], $column_aliases = [], $custom_columns = [], $sorting = [], $grouping = [], $limits = [], $custom_tables = []) {
    print_rr([
        'tables' => $tables,
        'joins' => $joins,
        'selected_columns' => $selected_columns,
        'filters' => $filters,
        'table_aliases' => $table_aliases,
        'column_aliases' => $column_aliases,
        'custom_columns' => $custom_columns,
        'sorting' => $sorting,
        'grouping' => $grouping,
        'limits' => $limits,
        'custom_tables' => $custom_tables
    ],'build_multi_table_query params');
    if (empty($tables)) {
        return "-- No tables selected";
    }

    $primary_table = $tables[0];

    // Build complete table alias mapping including join aliases
    $all_table_aliases = $table_aliases; // Start with explicit table aliases

    // Add primary table alias if it exists
    if (isset($table_aliases[$primary_table])) {
        $all_table_aliases[$primary_table] = $table_aliases[$primary_table];
    }

    // Pre-populate join aliases so they're available for SELECT clause building
    foreach ($joins as $join) {
        if (!empty($join['left_alias']) && !empty($join['left_table'])) {
            $all_table_aliases[$join['left_table']] = $join['left_alias'];
        }
        if (!empty($join['right_alias']) && !empty($join['right_table'])) {
            $all_table_aliases[$join['right_table']] = $join['right_alias'];
        }
    }

    // Build SELECT clause
    $select_parts = [];

    // Add regular columns
    if (!empty($selected_columns)) {
        foreach ($selected_columns as $table_or_alias => $columns) {
            // Determine if this is a table name or alias
            $table_ref = $table_or_alias;
            $actual_table = $table_or_alias;

            // Check if this is an alias by looking in the all_table_aliases map
            $is_alias = false;
            foreach ($all_table_aliases as $table_name => $alias) {
                if ($alias === $table_or_alias) {
                    $actual_table = $table_name;
                    $is_alias = true;
                    break;
                }
            }

            // If not found as an alias, check if it's a table name with an alias
            if (!$is_alias && isset($all_table_aliases[$table_or_alias])) {
                $table_ref = $all_table_aliases[$table_or_alias];
            }

            foreach ($columns as $column) {
                $column_key = $table_or_alias . '.' . $column;

                // Check if there's a custom column alias
                if (isset($column_aliases[$column_key]) && !empty($column_aliases[$column_key])) {
                    $column_alias = $column_aliases[$column_key];
                    $select_parts[] = "`{$table_ref}`.`{$column}` AS `{$column_alias}`";
                } else {
                    // Use default alias format (table_or_alias_column)
                    $alias_suffix = $table_or_alias;
                    $select_parts[] = "`{$table_ref}`.`{$column}` AS `{$alias_suffix}_{$column}`";
                }
            }
        }
    }

    // Add custom columns
    if (!empty($custom_columns)) {
        foreach ($custom_columns as $custom_column) {
            if (!empty($custom_column['sql']) && !empty($custom_column['alias'])) {
                $select_parts[] = "({$custom_column['sql']}) AS `{$custom_column['alias']}`";
            }
        }
    }

    // If no columns selected, use *
    $select = empty($select_parts) ? "*" : implode(', ', $select_parts);

    // Build FROM clause with primary table alias if available
    $primary_table_ref = isset($all_table_aliases[$primary_table]) ? "`{$primary_table}` AS `{$all_table_aliases[$primary_table]}`" : "`{$primary_table}`";
    $query = "SELECT {$select} FROM {$primary_table_ref}";

    // Add joins with aliases
    foreach ($joins as $join) {
        $join_type = strtoupper($join['type'] ?? 'INNER');
        $join_table = $join['right_table'] ?? $join['table']; // Support both formats
        $left_column = $join['left_column'];
        $right_column = $join['right_column'];

        // Build table reference with alias if provided
        $join_table_ref = !empty($join['right_alias']) ? "`{$join_table}` AS `{$join['right_alias']}`" : "`{$join_table}`";

        // Format column references with proper backticks and aliases
        $formatted_left = $left_column;
        $formatted_right = $right_column;

        if (strpos($left_column, '.') !== false) {
            $parts = explode('.', $left_column);
            $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
            $formatted_left = "`{$table_ref}`.`{$parts[1]}`";
        }

        if (strpos($right_column, '.') !== false) {
            $parts = explode('.', $right_column);
            // For right column, use the alias being defined in this join if it matches the table
            if ($parts[0] === $join_table && !empty($join['right_alias'])) {
                $table_ref = $join['right_alias'];
            } else {
                $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
            }
            $formatted_right = "`{$table_ref}`.`{$parts[1]}`";
        }

        $query .= " {$join_type} JOIN {$join_table_ref} ON {$formatted_left} = {$formatted_right}";
    }

    // Add custom tables (subqueries and complex joins)
    if (!empty($custom_tables)) {
        foreach ($custom_tables as $custom_table) {
            if (!empty($custom_table['sql']) && !empty($custom_table['alias'])) {
                $join_type = strtoupper($custom_table['join_type'] ?? 'LEFT JOIN');
                $table_alias = $custom_table['alias'];
                $table_sql = $custom_table['sql'];
                $join_condition = $custom_table['join_condition'] ?? '';

                // Add the custom table as a join
                $query .= " {$join_type} ({$table_sql}) AS `{$table_alias}`";

                // Add join condition if provided
                if (!empty($join_condition)) {
                    $query .= " ON {$join_condition}";
                }
            }
        }
    }

    // Add WHERE conditions
    if (!empty($filters)) {
        $where_conditions = [];

        foreach ($filters as $filter) {
            // Handle OR conditions for search
            if (isset($filter['type']) && $filter['type'] === 'OR' && isset($filter['conditions'])) {
                $or_conditions = [];
                foreach ($filter['conditions'] as $or_filter) {
                    $or_condition = build_filter_condition($or_filter, $all_table_aliases, $primary_table);
                    if ($or_condition) {
                        $or_conditions[] = $or_condition;
                    }
                }
                if (!empty($or_conditions)) {
                    $where_conditions[] = '(' . implode(' OR ', $or_conditions) . ')';
                }
            } else {
                // Regular filter condition
                $condition = build_filter_condition($filter, $all_table_aliases, $primary_table);
                if ($condition) {
                    $where_conditions[] = $condition;
                }
            }
        }

        if (!empty($where_conditions)) {
            $query .= " WHERE " . implode(' AND ', $where_conditions);
        }
    }

    // Add GROUP BY clause
    if (!empty($grouping)) {
        $group_parts = [];
        foreach ($grouping as $group) {
            if (!empty($group['column'])) {
                $column = $group['column'];

                // Handle table.column format with aliases
                if (strpos($column, '.') === false) {
                    $primary_table_ref = isset($all_table_aliases[$primary_table]) ? $all_table_aliases[$primary_table] : $primary_table;
                    $column = "`{$primary_table_ref}`.`{$column}`";
                } else {
                    $parts = explode('.', $column);
                    $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
                    $column = "`{$table_ref}`.`{$parts[1]}`";
                }

                $group_parts[] = $column;
            }
        }

        if (!empty($group_parts)) {
            $query .= " GROUP BY " . implode(', ', $group_parts);
        }
    }

    // Add ORDER BY clause
    if (!empty($sorting)) {
        $order_parts = [];
        foreach ($sorting as $sort) {
            if (!empty($sort['column']) && !empty($sort['direction'])) {
                $column = $sort['column'];
                $direction = strtoupper($sort['direction']);

                // Validate direction
                if (!in_array($direction, ['ASC', 'DESC'])) {
                    $direction = 'ASC';
                }

                // Handle table.column format with aliases for ORDER BY
                if (strpos($column, '.') === false) {
                    // If no table prefix, assume it's a column alias or custom column
                    $order_parts[] = "`{$column}` {$direction}";
                } else {
                    $parts = explode('.', $column);
                    $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
                    $order_parts[] = "`{$table_ref}`.`{$parts[1]}` {$direction}";
                }
            }
        }

        if (!empty($order_parts)) {
            $query .= " ORDER BY " . implode(', ', $order_parts);
        }
    }

    // Add LIMIT clause
    if (!empty($limits) && ($limits['enabled'] ?? false)) {
        if (!empty($limits['limit']) && is_numeric($limits['limit'])) {
            $limit = (int)$limits['limit'];
            $query .= " LIMIT {$limit}";

            // Add OFFSET if specified
            if (!empty($limits['offset']) && is_numeric($limits['offset'])) {
                $offset = (int)$limits['offset'];
                $query .= " OFFSET {$offset}";
            }
        }
    }

    return $query;
}

/**
 * Build individual filter condition
 */
function build_filter_condition($filter, $all_table_aliases, $primary_table) {
    if (empty($filter['column']) || empty($filter['operator'])) {
        return null;
    }

    $column = $filter['column'];
    $operator = $filter['operator'];
    $value = $filter['value'] ?? '';

    // Handle table.column format with aliases
    if (strpos($column, '.') === false) {
        $primary_table_ref = isset($all_table_aliases[$primary_table]) ? $all_table_aliases[$primary_table] : $primary_table;
        $column = "`{$primary_table_ref}`.`{$column}`";
    } else {
        $parts = explode('.', $column);
        $table_ref = isset($all_table_aliases[$parts[0]]) ? $all_table_aliases[$parts[0]] : $parts[0];
        $column = "`{$table_ref}`.`{$parts[1]}`";
    }

    switch ($operator) {
        case 'IS NULL':
            return "{$column} IS NULL";
        case 'IS NOT NULL':
            return "{$column} IS NOT NULL";
        case 'IN':
            $values = array_map('trim', explode(',', $value));
            $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
            return "{$column} IN (" . implode(', ', $quoted_values) . ")";
        case 'NOT IN':
            $values = array_map('trim', explode(',', $value));
            $quoted_values = array_map(function($v) { return "'" . addslashes($v) . "'"; }, $values);
            return "{$column} NOT IN (" . implode(', ', $quoted_values) . ")";
        case 'LIKE':
            return "{$column} LIKE '" . addslashes($value) . "'";
        case 'NOT LIKE':
            return "{$column} NOT LIKE '" . addslashes($value) . "'";
        default:
            return "{$column} {$operator} '" . addslashes($value) . "'";
    }
}

/**
 * Remove table (returns empty content for HTMX plus OOB updates)
 */
function remove_table($params) {
    $table_name = $params['table_name'] ?? '';
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    // Remove the table from selected tables
    $updated_tables = array_filter($selected_tables, function($table) use ($table_name) {
        return $table !== $table_name;
    });
    $updated_tables = array_values($updated_tables); // Re-index array

    try {
        // Generate updated join configuration
        $join_config = Edge::render('data-source-join-configuration', [
            'selected_tables' => $updated_tables,
            'table_columns' => [],
            'existing_joins' => []
        ]);

        // Generate updated column selection using the alias-aware function
        $column_params = [
            'selected_tables' => json_encode($updated_tables),
            'selected_columns' => json_encode([]),
            'joins' => json_encode([]),
            'table_aliases' => [],
            'column_aliases' => []
        ];

        $column_selection = column_selection_fragment($column_params);

        // Generate updated query preview
        if (!empty($updated_tables)) {
            $query = build_multi_table_query($updated_tables, [], [], [], [], [], [], [], [], []);
            $query_preview = Edge::render('data-source-query-preview', [
                'query' => $query,
                'table_name' => implode(', ', $updated_tables),
                'filters' => [],
                'tables' => $updated_tables,
                'joins' => [],
                'selected_columns' => []
            ]);
        } else {
            $query_preview = '<div class="text-center py-8 text-gray-500">Select tables to see the SQL query</div>';
        }

        // Generate updated sections that depend on available columns (empty since no columns selected after table removal)
        $updated_params = array_merge($params, ['selected_tables' => json_encode($updated_tables)]);
        $updated_grouping = generate_updated_grouping_section($updated_params);
        $updated_sorting = generate_updated_sorting_section($updated_params);
        $updated_filters = generate_updated_filters_section($updated_params);

        // Return empty content for the removed table plus OOB swaps
        return '' .
               '<div id="join-configuration-container" hx-swap-oob="innerHTML">' . $join_config . '</div>' .
               '<div id="column-selection-container" hx-swap-oob="innerHTML">' . $column_selection . '</div>' .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Select all columns for all tables
 */
function select_all_columns($params) {
    // Parse selected_tables parameter
    $selected_tables = [];
    if (isset($params['selected_tables'])) {
        if (is_array($params['selected_tables'])) {
            $selected_tables = $params['selected_tables'];
        } elseif (is_string($params['selected_tables']) && !empty($params['selected_tables'])) {
            $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        }
    }

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        // Parse joins to get table aliases
        $joins = [];
        if (isset($params['joins'])) {
            if (is_array($params['joins'])) {
                $joins = $params['joins'];
            } elseif (is_string($params['joins']) && !empty($params['joins'])) {
                $joins = json_decode($params['joins'], true) ?: [];
            }
        }

        // Build a map of table aliases from joins
        $table_aliases_map = [];
        foreach ($joins as $join) {
            if (!empty($join['left_alias']) && !empty($join['left_table'])) {
                $table_aliases_map[$join['left_table']][] = $join['left_alias'];
            }
            if (!empty($join['right_alias']) && !empty($join['right_table'])) {
                $table_aliases_map[$join['right_table']][] = $join['right_alias'];
            }
        }

        // Get all columns for all tables, including aliases
        $all_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                // Check if this table has aliases
                if (isset($table_aliases_map[$table_name]) && !empty($table_aliases_map[$table_name])) {
                    // Add columns for each alias
                    foreach ($table_aliases_map[$table_name] as $alias) {
                        foreach ($table_info['columns'] as $column) {
                            $all_columns[] = $alias . '.' . $column['Field'];
                        }
                    }
                } else {
                    // No aliases, use table name directly
                    foreach ($table_info['columns'] as $column) {
                        $all_columns[] = $table_name . '.' . $column['Field'];
                    }
                }
            }
        }

        // Use the updated column_selection_fragment to render with alias support
        return column_selection_fragment(array_merge($params, [
            'selected_columns' => json_encode($all_columns)
        ]));

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Select All Columns Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Clear all column selections
 */
function clear_all_columns($params) {
    // Parse selected_tables parameter
    $selected_tables = [];
    if (isset($params['selected_tables'])) {
        if (is_array($params['selected_tables'])) {
            $selected_tables = $params['selected_tables'];
        } elseif (is_string($params['selected_tables']) && !empty($params['selected_tables'])) {
            $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        }
    }

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        // Use the updated column_selection_fragment to render with alias support
        return column_selection_fragment(array_merge($params, [
            'selected_columns' => json_encode([])
        ]));

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Clear All Columns Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Select all columns for a specific table
 */
function select_table_columns($params) {
    // Parse selected_tables parameter
    $selected_tables = [];
    if (isset($params['selected_tables'])) {
        if (is_array($params['selected_tables'])) {
            $selected_tables = $params['selected_tables'];
        } elseif (is_string($params['selected_tables']) && !empty($params['selected_tables'])) {
            $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        }
    }

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    $target_table = $params['table_name'] ?? '';
    $table_alias = $params['table_alias'] ?? '';

    // Parse selected_columns_json parameter
    $current_selected = [];
    if (isset($params['selected_columns_json'])) {
        if (is_array($params['selected_columns_json'])) {
            $current_selected = $params['selected_columns_json'];
        } elseif (is_string($params['selected_columns_json']) && !empty($params['selected_columns_json'])) {
            $current_selected = json_decode($params['selected_columns_json'], true) ?: [];
        }
    }

    if (empty($selected_tables) || empty($target_table)) {
        return '<div class="text-gray-500">Invalid request</div>';
    }

    // Use alias if provided, otherwise use table name
    $table_ref = $table_alias ?: $target_table;

    try {
        // Remove existing selections for this table/alias combination
        $updated_selected = array_filter($current_selected, function($col) use ($table_ref) {
            return !str_starts_with($col, $table_ref . '.');
        });

        // Add all columns for target table using the appropriate reference
        $table_info = data_source_manager::get_table_info($target_table);
        if ($table_info) {
            foreach ($table_info['columns'] as $column) {
                $updated_selected[] = $table_ref . '.' . $column['Field'];
            }
        }

        // Use the updated column_selection_fragment to render with alias support
        return column_selection_fragment(array_merge($params, [
            'selected_columns' => json_encode($updated_selected)
        ]));

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Select Table Columns Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Clear all columns for a specific table
 */
function clear_table_columns($params) {
    // Parse selected_tables parameter
    $selected_tables = [];
    if (isset($params['selected_tables'])) {
        if (is_array($params['selected_tables'])) {
            $selected_tables = $params['selected_tables'];
        } elseif (is_string($params['selected_tables']) && !empty($params['selected_tables'])) {
            $selected_tables = json_decode($params['selected_tables'], true) ?: [];
        }
    }

    $target_table = $params['table_name'] ?? '';
    $table_alias = $params['table_alias'] ?? '';

    // Parse selected_columns_json parameter
    $current_selected = [];
    if (isset($params['selected_columns_json'])) {
        if (is_array($params['selected_columns_json'])) {
            $current_selected = $params['selected_columns_json'];
        } elseif (is_string($params['selected_columns_json']) && !empty($params['selected_columns_json'])) {
            $current_selected = json_decode($params['selected_columns_json'], true) ?: [];
        }
    }

    if (empty($selected_tables) || empty($target_table)) {
        return '<div class="text-gray-500">Invalid request</div>';
    }

    // Use alias if provided, otherwise use table name
    $table_ref = $table_alias ?: $target_table;

    try {
        // Remove selections for this table/alias combination
        $updated_selected = array_filter($current_selected, function($col) use ($table_ref) {
            return !str_starts_with($col, $table_ref . '.');
        });

        // Use the updated column_selection_fragment to render with alias support
        return column_selection_fragment(array_merge($params, [
            'selected_columns' => json_encode(array_values($updated_selected))
        ]));

    } catch (Exception $e) {
        // Return error with HX-Reswap header to prevent replacement
        if (function_exists('header')) {
            header('HX-Reswap: none');
        }
        return '<div class="text-red-500 p-4 border border-red-300 rounded-md bg-red-50">
                    <strong>Clear Table Columns Error:</strong> ' . htmlspecialchars($e->getMessage()) . '
                </div>';
    }
}

/**
 * Add a new filter row (calculates index server-side)
 */
function add_filter_row($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    // Additional fallback: if selected_tables param exists but is malformed (like just "["), use tables array
    if (empty($selected_tables) && isset($params['selected_tables']) && $params['selected_tables'] === '[') {
        if (isset($params['tables']) && is_array($params['tables'])) {
            $selected_tables = array_values($params['tables']);
        }
    }

    // Calculate filter index by counting existing filters in the form
    $filter_index = 0;
    if (isset($params['filters']) && is_array($params['filters'])) {
        $filter_index = count($params['filters']);
    }

    if (empty($selected_tables)) {
        return '<div class="text-red-500">Please select tables first</div>';
    }

    try {
        // Get columns from the first table (primary table)
        $primary_table = $selected_tables[0];
        $available_tables = data_source_manager::get_available_tables();
        $columns = [];

        foreach ($available_tables as $table) {
            if ($table['name'] === $primary_table) {
                $columns = $table['columns'];
                break;
            }
        }

        // Filter operators
        $filter_operators = [
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'LIKE' => 'Contains',
            'NOT LIKE' => 'Does Not Contain',
            'IN' => 'In List',
            'NOT IN' => 'Not In List',
            'IS NULL' => 'Is Empty',
            'IS NOT NULL' => 'Is Not Empty'
        ];

        return Edge::render('data-source-filter-row', [
            'columns' => $columns,
            'filter_operators' => $filter_operators,
            'filter_index' => $filter_index,
            'filter' => [
                'column' => '',
                'operator' => '=',
                'value' => ''
            ]
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error creating filter: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Add a new table (calculates index server-side)
 */
function add_table($params) {
    $table_name = $params['add_table'] ?? '';
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    if (empty($table_name)) {
        return '<div class="text-red-500">Please select a table</div>';
    }

    if (is_array($selected_tables) && in_array($table_name, $selected_tables)) {
        return '<div class="text-red-500">Table already selected</div>';
    }

    $table_index = count($selected_tables);

    try {
        $available_tables = data_source_manager::get_available_tables();
        $table_info = null;

        foreach ($available_tables as $table) {
            if ($table['name'] === $table_name) {
                $table_info = $table;
                break;
            }
        }

        if (!$table_info) {
            return '<div class="text-red-500">Table not found</div>';
        }

        // Generate the table item
        $table_item = Edge::render('data-source-table-item', [
            'table_info' => $table_info,
            'table_index' => $table_index,
            'is_primary' => $table_index == 0,
            'table_alias' => '' // New tables start with empty alias
        ]);

        // Update selected tables array for dependent sections
        $updated_tables = array_merge($selected_tables, [$table_name]);

        // Generate updated join configuration
        $join_config = Edge::render('data-source-join-configuration', [
            'selected_tables' => $updated_tables,
            'table_columns' => [],
            'existing_joins' => []
        ]);

        // Generate updated column selection using the alias-aware function
        $column_params = [
            'selected_tables' => json_encode($updated_tables),
            'selected_columns' => json_encode([]),
            'joins' => json_encode([]),
            'table_aliases' => [],
            'column_aliases' => []
        ];

        $column_selection = column_selection_fragment($column_params);

        // Generate updated query preview
        $query = build_multi_table_query($updated_tables, [], [], [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $updated_tables),
            'filters' => [],
            'tables' => $updated_tables,
            'joins' => [],
            'selected_columns' => []
        ]);

        // Generate updated sections that depend on available columns (initially empty since no columns selected yet)
        $updated_params = array_merge($params, ['selected_tables' => json_encode($updated_tables)]);
        $updated_grouping = generate_updated_grouping_section($updated_params);
        $updated_sorting = generate_updated_sorting_section($updated_params);
        $updated_filters = generate_updated_filters_section($updated_params);

        // Return main content plus OOB swaps
        return $table_item .
               '<div id="join-configuration-container" hx-swap-oob="innerHTML">' . $join_config . '</div>' .
               '<div id="column-selection-container" hx-swap-oob="innerHTML">' . $column_selection . '</div>' .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding table: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Select all columns with query preview update
 */
function select_all_columns_with_preview($params) {
    $column_selection = select_all_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get all columns for query preview
        $all_columns = [];
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = [];
                foreach ($table_info['columns'] as $column) {
                    $all_columns[] = $table_name . '.' . $column['Field'];
                    $table_columns[$table_name][] = $column['Field'];
                }
            }
        }

        $query = build_multi_table_query($selected_tables, [], $table_columns, [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => $table_columns
        ]);

        // Generate updated sections that depend on available columns
        $updated_params = array_merge($params, ['selected_columns' => $all_columns]);
        $updated_grouping = generate_updated_grouping_section($updated_params);
        $updated_sorting = generate_updated_sorting_section($updated_params);
        $updated_filters = generate_updated_filters_section($updated_params);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';
    }

    return $column_selection;
}

/**
 * Clear all columns with query preview update
 */
function clear_all_columns_with_preview($params) {
    $column_selection = clear_all_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        $query = build_multi_table_query($selected_tables, [], [], [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => []
        ]);

        // Generate updated sections that depend on available columns (empty since no columns selected)
        $updated_params = array_merge($params, ['selected_columns' => []]);
        $updated_grouping = generate_updated_grouping_section($updated_params);
        $updated_sorting = generate_updated_sorting_section($updated_params);
        $updated_filters = generate_updated_filters_section($updated_params);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';
    }

    return $column_selection;
}

/**
 * Update column selection and query preview
 */
function update_column_selection($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    // Additional fallback: if selected_tables param exists but is malformed (like just "["), use tables array
    if (empty($selected_tables) && isset($params['selected_tables']) && $params['selected_tables'] === '[') {
        if (isset($params['tables']) && is_array($params['tables'])) {
            $selected_tables = array_values($params['tables']);
        }
    }

    $selected_columns = $params['selected_columns'] ?? [];
    $column_aliases = $params['column_aliases'] ?? [];

    // Debug logging
    tcs_log("update_column_selection - selected_tables param: " . ($params['selected_tables'] ?? 'null'), 'data_source_debug');
    tcs_log("update_column_selection - tables param: " . json_encode($params['tables'] ?? []), 'data_source_debug');
    tcs_log("update_column_selection - final selected_tables: " . json_encode($selected_tables), 'data_source_debug');

    if (empty($selected_tables)) {
        return '<div class="text-gray-500">Add tables to select columns</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        // Generate updated column selection using the alias-aware function
        $column_selection = column_selection_fragment($params);

        // Generate updated query preview
        $query_preview = query_preview_fragment($params);

        // Generate updated data preview
        $data_preview = data_preview_fragment($params);

        // Generate updated sections that depend on available columns
        $updated_grouping = generate_updated_grouping_section($params);
        $updated_sorting = generate_updated_sorting_section($params);
        $updated_filters = generate_updated_filters_section($params);

        // Return main content plus OOB swaps for previews and dependent sections
        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="preview-container" hx-swap-oob="innerHTML">' . $data_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating column selection: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Update column alias and both previews
 */
function update_column_alias($params) {
    try {
        // Generate updated query preview
        $query_preview = query_preview_fragment($params);

        // Generate updated data preview
        $data_preview = data_preview_fragment($params);

        // Return query preview as main content plus OOB swap for data preview
        return $query_preview .
               '<div id="preview-container" hx-swap-oob="innerHTML">' . $data_preview . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating column alias: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Update dependent sections when custom columns or column selection changes
 */
function update_dependent_sections($params) {
    try {
        // Generate updated query preview
        $query_preview = query_preview_fragment($params);

        // Generate updated data preview
        $data_preview = data_preview_fragment($params);

        // Generate updated sections that depend on available columns
        $updated_grouping = generate_updated_grouping_section($params);
        $updated_sorting = generate_updated_sorting_section($params);
        $updated_filters = generate_updated_filters_section($params);

        // Return query preview as main content plus OOB swaps for all dependent sections
        return $query_preview .
               '<div id="preview-container" hx-swap-oob="innerHTML">' . $data_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating dependent sections: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Add a new custom column row
 */
function add_custom_column($params) {
    try {
        // Get current custom columns to determine the next index
        $existing_custom_columns = $params['custom_columns'] ?? [];
        $next_index = is_array($existing_custom_columns) ? count($existing_custom_columns) : 0;

        // Create a new custom column with default values
        $new_custom_column = [
            'sql' => '',
            'alias' => ''
        ];

        // Generate the custom column item
        $custom_column_item = Edge::render('data-source-custom-column-item', [
            'custom_column' => $new_custom_column,
            'column_index' => $next_index
        ]);

        // Generate updated sections that depend on available columns
        $updated_grouping = generate_updated_grouping_section($params);
        $updated_sorting = generate_updated_sorting_section($params);
        $updated_filters = generate_updated_filters_section($params);

        // Return main content plus OOB swaps for dependent sections
        return $custom_column_item .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding custom column: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove a custom column row
 */
function remove_custom_column($params) {
    try {
        // Generate updated query preview after removal
        $query_preview = query_preview_fragment($params);

        // Generate updated sections that depend on available columns
        $updated_grouping = generate_updated_grouping_section($params);
        $updated_sorting = generate_updated_sorting_section($params);
        $updated_filters = generate_updated_filters_section($params);

        // Return empty content for the removed custom column plus OOB swaps for dependent sections
        return '' .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>' .
               '<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_grouping . '</div>' .
               '<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">' . $updated_sorting . '</div>' .
               '<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">' . $updated_filters . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Update data source type and return appropriate configuration UI
 */
function update_data_source_type($params) {
    $data_source_type = $params['data_source_type'] ?? 'standard';
    $available_tables = data_source_manager::get_available_tables();

    if ($data_source_type === 'multi_table_merger') {
        return Edge::render('multi-table-merger-configuration', [
            'available_tables' => $available_tables,
            'data_source' => []
        ]);
    } else {
        // Return standard table selection
        return Edge::render('data-source-table-selection', [
            'available_tables' => $available_tables,
            'selected_tables' => [],
            'table_aliases' => []
        ]);
    }
}

/**
 * Add wildcard pattern for multi-table merger
 */
function add_wildcard_pattern($params) {
    $pattern = trim($params['new-pattern-input'] ?? '');
    $existing_patterns = json_decode($params['table_patterns'] ?? '[]', true) ?: [];

    if (empty($pattern)) {
        return '<div class="text-red-500 text-sm">Please enter a pattern</div>';
    }

    if (in_array($pattern, $existing_patterns)) {
        return '<div class="text-red-500 text-sm">Pattern already exists</div>';
    }

    $pattern_index = count($existing_patterns);
    $available_tables = data_source_manager::get_available_tables();

    return Edge::render('wildcard-pattern-item', [
        'pattern' => $pattern,
        'pattern_index' => $pattern_index,
        'available_tables' => $available_tables
    ]);
}

/**
 * Remove wildcard pattern
 */
function remove_wildcard_pattern($params) {
    return ''; // Return empty content to remove the element
}

/**
 * Update pattern matches when pattern is modified
 */
function update_pattern_matches($params) {
    $pattern_index = $params['pattern_index'] ?? 0;
    $patterns = is_string($params['table_patterns']) ? json_decode($params['table_patterns']) : (is_array($params['table_patterns']) ?? []);
    $pattern = $patterns[$pattern_index] ?? '';
    print_rr(['pattern_index' => $pattern_index,'patterns' => $patterns, 'pattern' => $pattern], 'Matching tables');
    if (empty($pattern)) {
        return '<div class="text-xs text-red-600">Pattern is empty</div>';
    }

    $available_tables = data_source_manager::get_available_tables();
    $matching_tables = [];
    $pattern_regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';

    foreach ($available_tables as $table) {
        if (preg_match($pattern_regex, $table['name'])) {
            $matching_tables[] = $table;
        }
    }
    print_rr([
        'available_tables' => $available_tables,
        'pattern' => $pattern,
        'regex' => $pattern_regex,
        'tables' => $matching_tables,
        $params
    ],
        'Matching tables'
    );
    if (count($matching_tables) > 0) {
        $html = '<div class="text-xs text-gray-600"><strong>Matching tables:</strong>';
        foreach ($matching_tables as $table) {
            $html .= '<span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded-md mr-1 mb-1">';
            $html .= htmlspecialchars($table['display_name']);
            $html .= '<span class="text-blue-600">(' . number_format($table['row_count']) . ')</span>';
            $html .= '</span>';
        }
        $html .= '</div>';
        return $html;
    } else {
        return '<div class="text-xs text-red-600">No matching tables found for pattern "' . htmlspecialchars($pattern) . '"</div>';
    }
}

/**
 * Add explicit table for multi-table merger
 */
function add_explicit_table($params) {
    $table_name = $params['add_explicit_table'] ?? '';
    $existing_tables = json_decode($params['explicit_tables'] ?? '[]', true) ?: [];

    if (empty($table_name)) {
        return '<div class="text-red-500 text-sm">Please select a table</div>';
    }

    if (in_array($table_name, $existing_tables)) {
        return '<div class="text-red-500 text-sm">Table already selected</div>';
    }

    $table_index = count($existing_tables);
    $available_tables = data_source_manager::get_available_tables();

    return Edge::render('explicit-table-item', [
        'table_name' => $table_name,
        'table_index' => $table_index,
        'available_tables' => $available_tables
    ]);
}

/**
 * Remove explicit table
 */
function remove_explicit_table($params) {
    return ''; // Return empty content to remove the element
}

/**
 * Update mapping method and return appropriate configuration UI
 */
function update_mapping_method($params) {
    $mapping_method = $params['mapping_method'] ?? 'like_for_like';

    if ($mapping_method === 'like_for_like') {
        return '<div class="text-center py-6 text-gray-500">No configuration required for like-for-like mapping.</div>';
    }

    $available_tables = data_source_manager::get_available_tables();

    // Get table patterns from both hidden input and individual pattern inputs
    $selected_table_patterns = json_decode($params['table_patterns'] ?? '[]', true) ?: [];

    // Also check for individual pattern inputs (table_patterns[0], table_patterns[1], etc.)
    if (empty($selected_table_patterns) && isset($params['table_patterns']) && is_array($params['table_patterns'])) {
        $selected_table_patterns = array_filter($params['table_patterns'], function($pattern) {
            return !empty(trim($pattern));
        });
    }



    // Get explicit tables from both hidden input and individual table inputs
    $selected_explicit_tables = json_decode($params['explicit_tables'] ?? '[]', true) ?: [];

    // Also check for individual table inputs (explicit_tables[0], explicit_tables[1], etc.)
    if (empty($selected_explicit_tables) && isset($params['explicit_tables']) && is_array($params['explicit_tables'])) {
        $selected_explicit_tables = array_filter($params['explicit_tables'], function($table) {
            return !empty(trim($table));
        });
    }

    $reference_table = $params['reference_table'] ?? '';

    return Edge::render('multi-table-column-mapping', [
        'mapping_method' => $mapping_method,
        'available_tables' => $available_tables, // This now includes fallback tables
        'selected_table_patterns' => $selected_table_patterns,
        'selected_explicit_tables' => $selected_explicit_tables,
        'reference_table' => $reference_table,
        'column_mappings' => [],
        'unified_mappings' => []
    ]);
}

/**
 * Add a new custom table row
 */
function add_custom_table($params) {
    try {
        // Get current custom tables to determine the next index
        $existing_custom_tables = $params['custom_tables'] ?? [];
        $next_index = is_array($existing_custom_tables) ? count($existing_custom_tables) : 0;



        // Hide "no custom tables" message
        $hide_message = '<script>
            const noCustomTablesMessage = document.getElementById("no-custom-tables-message");
            if (noCustomTablesMessage) {
                noCustomTablesMessage.style.display = "none";
            }
        </script>';

        // Generate updated query preview
        $query_preview = query_preview_fragment($params);

        return Edge::render('data-source-custom-table-row', [
            'custom_table_index' => $next_index,
            'custom_table' => []
        ]) . $hide_message .
        '<div hx-swap-oob="innerHTML:#query-preview-container">' . $query_preview . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove a custom table row
 */
function remove_custom_table($params) {
    try {
        // Generate updated query preview after removal
        $query_preview = query_preview_fragment($params);

        // Return empty content for the removed custom table plus OOB swap for query preview
        return '' .
            '<div hx-swap-oob="innerHTML:#query-preview-container">' . $query_preview . '</div>';

    } catch (Exception $e) {
        return '<div class="text-red-500">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}
/**
 * Update reference table and refresh column mapping UI
 */
function update_reference_table($params) {
    $mapping_method = $params['mapping_method'] ?? 'manual';
    $available_tables = data_source_manager::get_available_tables();

    // Get table patterns from both hidden input and individual pattern inputs
    $selected_table_patterns = json_decode($params['table_patterns'] ?? '[]', true) ?: [];
    if (empty($selected_table_patterns) && isset($params['table_patterns']) && is_array($params['table_patterns'])) {
        $selected_table_patterns = array_filter($params['table_patterns'], function($pattern) {
            return !empty(trim($pattern));
        });
    }

    // Get explicit tables from both hidden input and individual table inputs
    $selected_explicit_tables = json_decode($params['explicit_tables'] ?? '[]', true) ?: [];
    if (empty($selected_explicit_tables) && isset($params['explicit_tables']) && is_array($params['explicit_tables'])) {
        $selected_explicit_tables = array_filter($params['explicit_tables'], function($table) {
            return !empty(trim($table));
        });
    }



    $reference_table = $params['reference_table'] ?? '';
    $column_mappings = json_decode($params['column_mappings'] ?? '[]', true) ?: [];
    $unified_mappings = json_decode($params['unified_mappings'] ?? '[]', true) ?: [];

    // Handle min_confidence parameter for unified field mapper
    if ($mapping_method === 'unified_field_mapper' && isset($params['min_confidence'])) {
        $unified_mappings['min_confidence'] = (int)$params['min_confidence'];
    }

    return Edge::render('multi-table-column-mapping', [
        'mapping_method' => $mapping_method,
        'available_tables' => $available_tables, // Now includes pattern tables
        'selected_table_patterns' => $selected_table_patterns,
        'selected_explicit_tables' => $selected_explicit_tables,
        'reference_table' => $reference_table,
        'column_mappings' => $column_mappings,
        'unified_mappings' => $unified_mappings
    ]);
}

/**
 * Add a new column mapping
 */
function add_column_mapping($params) {
    $existing_mappings = json_decode($params['column_mappings'] ?? '[]', true) ?: [];
    $mapping_index = count($existing_mappings);

    // Get all available columns
    $available_tables = data_source_manager::get_available_tables();
    $selected_table_patterns = json_decode($params['table_patterns'] ?? '[]', true) ?: [];
    $selected_explicit_tables = json_decode($params['explicit_tables'] ?? '[]', true) ?: [];
    $reference_table = $params['reference_table'] ?? '';

    // Build included tables list
    $included_tables = [];
    foreach ($selected_table_patterns as $pattern) {
        $pattern_regex = '/^' . str_replace('*', '.*', preg_quote($pattern, '/')) . '$/i';
        foreach ($available_tables as $table) {
            if (preg_match($pattern_regex, $table['name']) && !in_array($table['name'], $included_tables)) {
                $included_tables[] = $table['name'];
            }
        }
    }
    foreach ($selected_explicit_tables as $table_name) {
        if (!in_array($table_name, $included_tables)) {
            $included_tables[] = $table_name;
        }
    }

    // Get all columns
    $all_columns = [];
    foreach ($included_tables as $table_name) {
        $table_info = data_source_manager::get_table_info($table_name);
        if ($table_info && !empty($table_info['columns'])) {
            foreach ($table_info['columns'] as $column) {
                $column_key = $table_name . '.' . $column['Field'];
                $all_columns[$column_key] = [
                    'table' => $table_name,
                    'column' => $column['Field'],
                    'type' => $column['Type'],
                    'display' => $table_name . '.' . $column['Field']
                ];
            }
        }
    }

    // Get reference columns
    $reference_columns = [];
    if (!empty($reference_table)) {
        $ref_table_info = data_source_manager::get_table_info($reference_table);
        if ($ref_table_info && !empty($ref_table_info['columns'])) {
            $reference_columns = $ref_table_info['columns'];
        }
    }

    return Edge::render('column-mapping-item', [
        'mapping' => [],
        'mapping_index' => $mapping_index,
        'all_columns' => $all_columns,
        'reference_columns' => $reference_columns
    ]);
}

/**
 * Remove a column mapping
 */
function remove_column_mapping($params) {
    return ''; // Return empty content to remove the element
}

/**
 * Add a source column to a mapping
 */
function add_source_column($params) {
    $mapping_index = $params['mapping_index'] ?? 0;
    $existing_mappings = $params['column_mappings'] ?? [];
    $current_mapping = is_array($existing_mappings) && isset($existing_mappings[$mapping_index]) ? $existing_mappings[$mapping_index] : [];
    $source_columns = $current_mapping['source_columns'] ?? [];

    // Add empty source column
    $source_columns[] = '';
    $current_mapping['source_columns'] = $source_columns;

    // Get all available columns (same logic as add_column_mapping)
    $available_tables = data_source_manager::get_available_tables();
    $selected_table_patterns = json_decode($params['table_patterns'] ?? '[]', true) ?: [];
    $selected_explicit_tables = json_decode($params['explicit_tables'] ?? '[]', true) ?: [];
    $reference_table = $params['reference_table'] ?? '';

    $included_tables = [];
    foreach ($selected_table_patterns as $pattern) {
        $pattern_regex = '/^' . str_replace('*', '.*', preg_quote($pattern, '/')) . '$/i';
        foreach ($available_tables as $table) {
            if (preg_match($pattern_regex, $table['name']) && !in_array($table['name'], $included_tables)) {
                $included_tables[] = $table['name'];
            }
        }
    }
    foreach ($selected_explicit_tables as $table_name) {
        if (!in_array($table_name, $included_tables)) {
            $included_tables[] = $table_name;
        }
    }

    $all_columns = [];
    foreach ($included_tables as $table_name) {
        $table_info = data_source_manager::get_table_info($table_name);
        if ($table_info && !empty($table_info['columns'])) {
            foreach ($table_info['columns'] as $column) {
                $column_key = $table_name . '.' . $column['Field'];
                $all_columns[$column_key] = [
                    'table' => $table_name,
                    'column' => $column['Field'],
                    'type' => $column['Type'],
                    'display' => $table_name . '.' . $column['Field']
                ];
            }
        }
    }

    $reference_columns = [];
    if (!empty($reference_table)) {
        $ref_table_info = data_source_manager::get_table_info($reference_table);
        if ($ref_table_info && !empty($ref_table_info['columns'])) {
            $reference_columns = $ref_table_info['columns'];
        }
    }

    return Edge::render('column-mapping-item', [
        'mapping' => $current_mapping,
        'mapping_index' => $mapping_index,
        'all_columns' => $all_columns,
        'reference_columns' => $reference_columns
    ]);
}

/**
 * Remove a source column from a mapping
 */
function remove_source_column($params) {
    $mapping_index = $params['mapping_index'] ?? 0;
    $column_index = $params['column_index'] ?? 0;
    $existing_mappings = $params['column_mappings'] ?? [];
    $current_mapping = is_array($existing_mappings) && isset($existing_mappings[$mapping_index]) ? $existing_mappings[$mapping_index] : [];
    $source_columns = $current_mapping['source_columns'] ?? [];

    // Remove the specified column
    if (isset($source_columns[$column_index])) {
        unset($source_columns[$column_index]);
        $source_columns = array_values($source_columns); // Re-index
    }
    $current_mapping['source_columns'] = $source_columns;

    // Return updated source columns section
    $html = '';
    if (empty($source_columns)) {
        $html = '<div class="text-sm text-gray-500" id="no-source-columns-' . $mapping_index . '">No source columns selected</div>';
    } else {
        // Get all available columns for the select options
        $available_tables = data_source_manager::get_available_tables();
        $selected_table_patterns = json_decode($params['table_patterns'] ?? '[]', true) ?: [];
        $selected_explicit_tables = json_decode($params['explicit_tables'] ?? '[]', true) ?: [];

        $included_tables = [];
        foreach ($selected_table_patterns as $pattern) {
            $pattern_regex = '/^' . str_replace('*', '.*', preg_quote($pattern, '/')) . '$/i';
            foreach ($available_tables as $table) {
                if (preg_match($pattern_regex, $table['name']) && !in_array($table['name'], $included_tables)) {
                    $included_tables[] = $table['name'];
                }
            }
        }
        foreach ($selected_explicit_tables as $table_name) {
            if (!in_array($table_name, $included_tables)) {
                $included_tables[] = $table_name;
            }
        }

        $source_column_options = ['' => 'Select source column...'];
        foreach ($included_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info && !empty($table_info['columns'])) {
                foreach ($table_info['columns'] as $column) {
                    $column_key = $table_name . '.' . $column['Field'];
                    $source_column_options[$column_key] = $table_name . '.' . $column['Field'] . ' (' . $column['Type'] . ')';
                }
            }
        }

        foreach ($source_columns as $col_index => $source_column) {
            $html .= '<div class="flex items-center space-x-2">';
            $html .= '<select name="column_mappings[' . $mapping_index . '][source_columns][' . $col_index . ']" class="text-sm flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">';
            foreach ($source_column_options as $value => $label) {
                $selected = ($value === $source_column) ? ' selected' : '';
                $html .= '<option value="' . htmlspecialchars($value) . '"' . $selected . '>' . htmlspecialchars($label) . '</option>';
            }
            $html .= '</select>';
            $html .= '<button type="button" hx-post="' . APP_ROOT . '/api/data_sources/remove_source_column" hx-target="#source-columns-' . $mapping_index . '" hx-swap="innerHTML" hx-include="form" hx-vals=\'{"mapping_index": "' . $mapping_index . '", "column_index": "' . $col_index . '"}\' class="p-1 text-red-600 hover:text-red-800">';
            $html .= '<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
            $html .= '</button>';
            $html .= '</div>';
        }
    }

    return $html;
}

/**
 * Preview merged data from multi-table merger
 */
function preview_merged_data($params) {
    try {
        $mapping_method = $params['mapping_method'] ?? 'like_for_like';

        // Handle table patterns (same logic as other functions)
        $selected_table_patterns = [];
        if (!empty($params['table_patterns']) && is_string($params['table_patterns'])) {
            $selected_table_patterns = json_decode($params['table_patterns'], true) ?: [];
        } elseif (isset($params['table_patterns']) && is_array($params['table_patterns'])) {
            $selected_table_patterns = array_filter($params['table_patterns'], function($pattern) {
                return !empty(trim($pattern));
            });
        }

        // Handle explicit tables
        $selected_explicit_tables = [];
        if (!empty($params['explicit_tables']) && is_string($params['explicit_tables'])) {
            $selected_explicit_tables = json_decode($params['explicit_tables'], true) ?: [];
        } elseif (isset($params['explicit_tables']) && is_array($params['explicit_tables'])) {
            $selected_explicit_tables = array_filter($params['explicit_tables'], function($table) {
                return !empty(trim($table));
            });
        }

        $column_mappings = json_decode($params['column_mappings'] ?? '[]', true) ?: [];
        $unified_mappings = json_decode($params['unified_mappings'] ?? '[]', true) ?: [];



        // Get included tables
        $available_tables = data_source_manager::get_available_tables();
        $included_tables = [];

        foreach ($selected_table_patterns as $pattern) {
            $pattern_regex = '/^' . str_replace('*', '.*', $pattern) . '$/i';
            foreach ($available_tables as $table) {
                if (preg_match($pattern_regex, $table['name']) && !in_array($table['name'], $included_tables)) {
                    $included_tables[] = $table['name'];
                }
            }
        }
        foreach ($selected_explicit_tables as $table_name) {
            if (!in_array($table_name, $included_tables)) {
                $included_tables[] = $table_name;
            }
        }

        if (empty($included_tables)) {
            return '<div class="text-center py-6 text-gray-500">No tables selected for merging.</div>';
        }

        // Get sample data from each table and merge according to method
        $merged_data = data_source_manager::preview_multi_table_merge(
            $included_tables,
            $mapping_method,
            $column_mappings,
            $unified_mappings,
            5 // Limit to 5 rows for preview
        );

        if (empty($merged_data)) {
            return '<div class="text-center py-6 text-gray-500">No data available for preview.</div>';
        }

        // Build preview table
        $html = '<div class="overflow-x-auto">';
        $html .= '<table class="min-w-full divide-y divide-gray-200">';
        $html .= '<thead class="bg-gray-50">';
        $html .= '<tr>';

        // Get column headers from first row
        $headers = array_keys($merged_data[0]);
        foreach ($headers as $header) {
            $html .= '<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">';
            $html .= htmlspecialchars($header);
            $html .= '</th>';
        }

        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody class="bg-white divide-y divide-gray-200">';

        foreach ($merged_data as $row) {
            $html .= '<tr>';
            foreach ($headers as $header) {
                $value = $row[$header] ?? '';
                $html .= '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">';
                $html .= htmlspecialchars(substr($value, 0, 100)); // Truncate long values
                if (strlen($value) > 100) {
                    $html .= '...';
                }
                $html .= '</td>';
            }
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        $html .= '<div class="mt-4 text-sm text-gray-600">';
        $html .= 'Showing ' . count($merged_data) . ' sample rows from ' . count($included_tables) . ' tables using ' . $mapping_method . ' mapping method.';
        $html .= '</div>';

        return $html;

    } catch (Exception $e) {
        return '<div class="text-red-500 p-4">Error generating preview: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}
/**
 * Add a new sort row
 */
function add_sort_row($params) {
    try {
        // Get current sorting to determine the next index
        $existing_sorting = $params['sorting'] ?? [];
        $next_index = is_array($existing_sorting) ? count($existing_sorting) : 0;

        // Get available columns for the dropdown
        $available_columns = get_available_columns_for_sorting($params);

        // Create a new sort with default values
        $new_sort = [
            'column' => '',
            'direction' => 'ASC'
        ];

        // Generate the sort item
        $sort_item = Edge::render('data-source-sorting-item', [
            'sort' => $new_sort,
            'sort_index' => $next_index,
            'available_columns' => $available_columns
        ]);

        return $sort_item;

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding sort: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove a sort row
 */
function remove_sort_row($params) {
    try {
        // Generate updated query preview after removal
        $query_preview = query_preview_fragment($params);

        // Return empty content for the removed sort plus OOB swap for query preview
        return '' . '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Add a new group row
 */
function add_group_row($params) {
    try {
        // Get current grouping to determine the next index
        $existing_grouping = $params['grouping'] ?? [];
        $next_index = is_array($existing_grouping) ? count($existing_grouping) : 0;

        // Get available columns for the dropdown
        $available_columns = get_available_columns_for_grouping($params);

        // Create a new group with default values
        $new_group = [
            'column' => ''
        ];

        // Generate the group item
        $group_item = Edge::render('data-source-grouping-item', [
            'group' => $new_group,
            'group_index' => $next_index,
            'available_columns' => $available_columns
        ]);

        return $group_item;

    } catch (Exception $e) {
        return '<div class="text-red-500">Error adding group: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove a group row
 */
function remove_group_row($params) {
    try {
        // Generate updated query preview after removal
        $query_preview = query_preview_fragment($params);

        // Return empty content for the removed group plus OOB swap for query preview
        return '' . '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';

    } catch (Exception $e) {
        // If there's an error, just return empty content
        return '';
    }
}

/**
 * Get all available columns from selected columns and custom columns
 */
function get_all_available_columns($params) {
    $available_columns = [];

    // Add regular selected columns
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        foreach ($params['selected_columns'] as $column) {
            $available_columns[] = $column;
        }
    }

    // Add custom columns
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (!empty($custom_column['alias'])) {
                $available_columns[] = $custom_column['alias'];
            }
        }
    }

    // Add custom table columns
    if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
        foreach ($params['custom_tables'] as $custom_table) {
            if (!empty($custom_table['alias']) && !empty($custom_table['sql'])) {
                $custom_columns = [];

                // Try to get columns from the columns field first
                if (!empty($custom_table['columns'])) {
                    $custom_columns = array_map('trim', explode(',', $custom_table['columns']));
                } else {
                    // If columns field is empty, try to extract from SQL
                    $custom_columns = extract_columns_from_sql($custom_table['sql']);
                }

                foreach ($custom_columns as $column_name) {
                    if (!empty($column_name)) {
                        $available_columns[] = $custom_table['alias'] . '.' . $column_name;
                    }
                }
            }
        }
    }

    return array_unique($available_columns);
}

/**
 * Get available columns for grouping (includes regular columns, aliased columns, and custom columns)
 */
function get_available_columns_for_grouping($params) {
    $available_columns = [];

    // Add regular selected columns
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        foreach ($params['selected_columns'] as $column) {
            // For grouping, we want the actual column names, not aliases
            $available_columns[] = $column;
        }
    }

    // Add custom columns (only those that don't use aggregate functions)
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (!empty($custom_column['alias']) && !empty($custom_column['sql'])) {
                // Check if the custom column uses aggregate functions
                $sql = strtoupper($custom_column['sql']);
                $aggregate_functions = ['COUNT(', 'SUM(', 'AVG(', 'MIN(', 'MAX(', 'GROUP_CONCAT('];
                $has_aggregate = false;

                foreach ($aggregate_functions as $func) {
                    if (strpos($sql, $func) !== false) {
                        $has_aggregate = true;
                        break;
                    }
                }

                // Only add non-aggregate custom columns to grouping options
                if (!$has_aggregate) {
                    $available_columns[] = $custom_column['alias'];
                }
            }
        }
    }

    // Add custom table columns
    if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
        foreach ($params['custom_tables'] as $custom_table) {
            if (!empty($custom_table['alias']) && !empty($custom_table['sql'])) {
                $custom_columns = [];

                // Try to get columns from the columns field first
                if (!empty($custom_table['columns'])) {
                    $custom_columns = array_map('trim', explode(',', $custom_table['columns']));
                } else {
                    // If columns field is empty, try to extract from SQL
                    $custom_columns = extract_columns_from_sql($custom_table['sql']);
                }

                foreach ($custom_columns as $column_name) {
                    if (!empty($column_name)) {
                        // For grouping, use the full column reference
                        $available_columns[] = $custom_table['alias'] . '.' . $column_name;
                    }
                }
            }
        }
    }

    return array_unique($available_columns);
}

/**
 * Generate updated grouping section with new available columns
 */
function generate_updated_grouping_section($params) {
    try {
        $available_columns = get_available_columns_for_grouping($params);
        $existing_grouping = $params['grouping'] ?? [];

        // Return just the container content
        if (empty($existing_grouping)) {
            return '<div id="no-grouping-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No grouping rules</h3>
                <p class="mt-1 text-sm text-gray-500">Results will not be grouped. Add columns to group by.</p>
            </div>';
        } else {
            $content = '';
            foreach ($existing_grouping as $index => $group) {
                $content .= Edge::render('data-source-grouping-item', [
                    'group' => $group,
                    'group_index' => $index,
                    'available_columns' => $available_columns
                ]);
            }
            return $content;
        }
    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating grouping: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Generate updated sorting section with new available columns
 */
function generate_updated_sorting_section($params) {
    try {
        $available_columns = get_available_columns_for_sorting($params);
        $existing_sorting = $params['sorting'] ?? [];

        // Return just the container content
        if (empty($existing_sorting)) {
            return '<div id="no-sorting-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No sorting rules</h3>
                <p class="mt-1 text-sm text-gray-500">Results will be returned in database default order.</p>
            </div>';
        } else {
            $content = '';
            foreach ($existing_sorting as $index => $sort) {
                $content .= Edge::render('data-source-sorting-item', [
                    'sort' => $sort,
                    'sort_index' => $index,
                    'available_columns' => $available_columns
                ]);
            }
            return $content;
        }
    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating sorting: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Generate updated filters section with new available columns
 */
function generate_updated_filters_section($params) {
    try {
        // Get available columns for filters (all columns including custom ones)
        $available_columns = get_all_available_columns($params);
        $existing_filters = $params['filters'] ?? [];

        // Get filter operators
        $filter_operators = [
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'LIKE' => 'Contains',
            'NOT LIKE' => 'Does Not Contain',
            'IN' => 'In List',
            'NOT IN' => 'Not In List',
            'IS NULL' => 'Is Empty',
            'IS NOT NULL' => 'Is Not Empty'
        ];

        // Convert column strings to the format expected by filter-row component
        $columns_for_filter = [];
        foreach ($available_columns as $column) {
            $columns_for_filter[] = ['Field' => $column];
        }

        // Generate filter rows
        $filter_rows = '';
        if (!empty($existing_filters)) {
            foreach ($existing_filters as $index => $filter) {
                $filter_rows .= Edge::render('data-source-filter-row', [
                    'columns' => $columns_for_filter,
                    'filter_operators' => $filter_operators,
                    'filter_index' => $index,
                    'filter' => $filter
                ]);
            }
        } else {
            $filter_rows = '<div class="text-center py-6 text-gray-500" id="no-filters-message">
                No filters configured. All data from the table will be included.
            </div>';
        }

        return $filter_rows;
    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating filters: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get available columns for sorting (includes regular columns, aliased columns, and custom columns)
 */
function get_available_columns_for_sorting($params) {
    $available_columns = [];

    // Add regular selected columns
    if (isset($params['selected_columns']) && is_array($params['selected_columns'])) {
        foreach ($params['selected_columns'] as $column) {
            // Check if there's a custom alias for this column
            if (isset($params['column_aliases'][$column]) && !empty($params['column_aliases'][$column])) {
                $available_columns[] = $params['column_aliases'][$column];
            } else {
                // Use default alias format
                if (strpos($column, '.') !== false) {
                    list($table, $col) = explode('.', $column, 2);
                    $available_columns[] = $table . '_' . $col;
                } else {
                    $available_columns[] = $column;
                }
            }
        }
    }

    // Add custom columns
    if (isset($params['custom_columns']) && is_array($params['custom_columns'])) {
        foreach ($params['custom_columns'] as $custom_column) {
            if (!empty($custom_column['alias'])) {
                $available_columns[] = $custom_column['alias'];
            }
        }
    }

    // Add custom table columns
    if (isset($params['custom_tables']) && is_array($params['custom_tables'])) {
        foreach ($params['custom_tables'] as $custom_table) {
            if (!empty($custom_table['alias']) && !empty($custom_table['sql'])) {
                $custom_columns = [];

                // Try to get columns from the columns field first
                if (!empty($custom_table['columns'])) {
                    $custom_columns = array_map('trim', explode(',', $custom_table['columns']));
                } else {
                    // If columns field is empty, try to extract from SQL
                    $custom_columns = extract_columns_from_sql($custom_table['sql']);
                }

                foreach ($custom_columns as $column_name) {
                    if (!empty($column_name)) {
                        // Use the same alias format as regular columns
                        $available_columns[] = $custom_table['alias'] . '_' . $column_name;
                    }
                }
            }
        }
    }

    return array_unique($available_columns);
}

/**
 * Update query preview based on current form state
 */
function update_query_preview($params) {
    return query_preview_fragment($params);
}

/**
 * Add a new join row
 */
function add_join_row($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    // Fallback: if selected_tables is empty or malformed, try to get from tables array
    if (empty($selected_tables) && isset($params['tables']) && is_array($params['tables'])) {
        $selected_tables = array_values($params['tables']);
    }

    $existing_joins = [];

    // Calculate join index by counting existing joins in the form
    $join_index = 0;
    if (isset($params['joins']) && is_array($params['joins'])) {
        $join_index = count($params['joins']);
    }

    if (count($selected_tables) < 2) {
        return '<div class="text-red-500">You need at least 2 tables to create a join</div>';
    }

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        // Create a new join with default values
        $new_join = [
            'type' => 'INNER',
            'left_table' => $selected_tables[0] ?? '',
            'left_column' => '',
            'right_table' => $selected_tables[1] ?? '',
            'right_column' => '',
            'left_alias' => '',
            'right_alias' => ''
        ];

        return Edge::render('data-source-join-item', [
            'join' => $new_join,
            'join_index' => $join_index,
            'selected_tables' => $selected_tables,
            'table_columns' => $table_columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error creating join: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Remove join row (returns empty content for HTMX)
 */
function remove_join_row($params) {
    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get current joins excluding the one being removed
        $current_joins = [];
        if (isset($params['joins']) && is_array($params['joins'])) {
            $remove_index = $params['join_index'] ?? -1;
            foreach ($params['joins'] as $index => $join_data) {
                if ($index != $remove_index && !empty($join_data['left_table']) && !empty($join_data['right_table']) &&
                    !empty($join_data['left_column']) && !empty($join_data['right_column'])) {

                    // Format column references properly
                    $left_column = $join_data['left_column'];
                    $right_column = $join_data['right_column'];

                    // If columns don't already have table prefix, add it
                    if (strpos($left_column, '.') === false) {
                        $left_column = $join_data['left_table'] . '.' . $left_column;
                    }
                    if (strpos($right_column, '.') === false) {
                        $right_column = $join_data['right_table'] . '.' . $right_column;
                    }

                    $current_joins[] = [
                        'type' => $join_data['type'] ?? 'INNER',
                        'table' => $join_data['right_table'],
                        'left_column' => $left_column,
                        'right_column' => $right_column,
                        'left_table' => $join_data['left_table'],
                        'right_table' => $join_data['right_table'],
                        'left_alias' => $join_data['left_alias'] ?? '',
                        'right_alias' => $join_data['right_alias'] ?? ''
                    ];
                }
            }
        }

        $query = build_multi_table_query($selected_tables, $current_joins, [], [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => $current_joins,
            'selected_columns' => []
        ]);

        return '' . '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return '';
}

/**
 * Update join columns when table selection changes
 */
function update_join_columns($params) {
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];
    $join_index = $params['join_index'] ?? 0;
    $field = $params['field'] ?? '';

    try {
        // Get column information for all selected tables
        $table_columns = [];
        foreach ($selected_tables as $table_name) {
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $table_columns[$table_name] = $table_info['columns'];
            }
        }

        // Get current join data from form
        $current_join = [
            'type' => $params['joins'][$join_index]['type'] ?? 'INNER',
            'left_table' => $params['joins'][$join_index]['left_table'] ?? '',
            'left_column' => $params['joins'][$join_index]['left_column'] ?? '',
            'right_table' => $params['joins'][$join_index]['right_table'] ?? '',
            'right_column' => $params['joins'][$join_index]['right_column'] ?? '',
            'left_alias' => $params['joins'][$join_index]['left_alias'] ?? '',
            'right_alias' => $params['joins'][$join_index]['right_alias'] ?? ''
        ];

        // Update the changed field
        if ($field === 'left_table') {
            $current_join['left_table'] = $params['joins'][$join_index]['left_table'] ?? '';
            $current_join['left_column'] = ''; // Reset column when table changes
        } elseif ($field === 'right_table') {
            $current_join['right_table'] = $params['joins'][$join_index]['right_table'] ?? '';
            $current_join['right_column'] = ''; // Reset column when table changes
        }

        return Edge::render('data-source-join-item', [
            'join' => $current_join,
            'join_index' => $join_index,
            'selected_tables' => $selected_tables,
            'table_columns' => $table_columns
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500">Error updating join: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Select table columns with query preview update
 */
function select_table_columns_with_preview($params) {
    $column_selection = select_table_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get updated selected columns from the result
        $target_table = $params['table_name'] ?? '';
        $current_selected = json_decode($params['selected_columns_json'] ?? '[]', true) ?: [];

        // Remove old selections for this table and add new ones
        $updated_selected = array_filter($current_selected, function($col) use ($target_table) {
            return !str_starts_with($col, $target_table . '.');
        });

        // Add all columns for target table
        $table_info = data_source_manager::get_table_info($target_table);
        if ($table_info) {
            foreach ($table_info['columns'] as $column) {
                $updated_selected[] = $target_table . '.' . $column['Field'];
            }
        }

        // Convert to table.column format for query building
        $selected_columns = [];
        foreach ($updated_selected as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        $query = build_multi_table_query($selected_tables, [], $selected_columns, [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => $selected_columns
        ]);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return $column_selection;
}

/**
 * Clear table columns with query preview update
 */
function clear_table_columns_with_preview($params) {
    $column_selection = clear_table_columns($params);

    // Generate updated query preview
    $selected_tables = json_decode($params['selected_tables'] ?? '[]', true) ?: [];

    if (!empty($selected_tables)) {
        // Get updated selected columns (with target table columns removed)
        $target_table = $params['table_name'] ?? '';
        $current_selected = json_decode($params['selected_columns_json'] ?? '[]', true) ?: [];

        $updated_selected = array_filter($current_selected, function($col) use ($target_table) {
            return !str_starts_with($col, $target_table . '.');
        });

        // Convert to table.column format for query building
        $selected_columns = [];
        foreach ($updated_selected as $column) {
            if (strpos($column, '.') !== false) {
                list($table, $col) = explode('.', $column, 2);
                $selected_columns[$table][] = $col;
            }
        }

        $query = build_multi_table_query($selected_tables, [], $selected_columns, [], [], [], [], [], [], []);
        $query_preview = Edge::render('data-source-query-preview', [
            'query' => $query,
            'table_name' => implode(', ', $selected_tables),
            'filters' => [],
            'tables' => $selected_tables,
            'joins' => [],
            'selected_columns' => $selected_columns
        ]);

        return $column_selection .
               '<div id="query-preview-container" hx-swap-oob="innerHTML">' . $query_preview . '</div>';
    }

    return $column_selection;
}

/**
 * Format SQL query for human-readable display
 */
function format_sql_query($query) {
    if (empty($query)) {
        return $query;
    }

    // Remove extra whitespace and normalize
    $query = preg_replace('/\s+/', ' ', trim($query));

    // Define SQL keywords that should start new lines
    $keywords = [
        'SELECT', 'FROM', 'WHERE', 'ORDER BY', 'GROUP BY', 'HAVING', 'LIMIT',
        'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'FULL JOIN', 'JOIN',
        'UNION', 'UNION ALL', 'AND', 'OR'
    ];

    // Sort keywords by length (longest first) to avoid partial matches
    usort($keywords, function($a, $b) {
        return strlen($b) - strlen($a);
    });

    $formatted = $query;

    // Add line breaks before major keywords
    foreach ($keywords as $keyword) {
        $pattern = '/\b' . preg_quote($keyword, '/') . '\b/i';

        if ($keyword === 'AND' || $keyword === 'OR') {
            // Special handling for AND/OR - add line break and indent
            $formatted = preg_replace($pattern, "\n  $keyword", $formatted);
        } elseif (strpos($keyword, 'JOIN') !== false) {
            // JOIN keywords get their own line with slight indent
            $formatted = preg_replace($pattern, "\n$keyword", $formatted);
        } else {
            // Other keywords start at the beginning of the line
            $formatted = preg_replace($pattern, "\n$keyword", $formatted);
        }
    }

    // Clean up the formatting
    $lines = explode("\n", $formatted);
    $formatted_lines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line)) {
            // Add proper indentation
            if (preg_match('/^(AND|OR)\b/i', $line)) {
                $formatted_lines[] = '  ' . $line;
            } elseif (preg_match('/^(INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|JOIN)\b/i', $line)) {
                $formatted_lines[] = $line;
            } else {
                $formatted_lines[] = $line;
            }
        }
    }

    // Join lines and add proper spacing
    $result = implode("\n", $formatted_lines);

    // Add extra spacing around major sections
    $result = preg_replace('/\n(FROM|WHERE|ORDER BY|GROUP BY|HAVING|LIMIT)\b/i', "\n\n$1", $result);

    // Clean up any double newlines at the start
    $result = ltrim($result, "\n");

    return $result;
}

/**
 * Execute a preview query and return formatted results
 */
function execute_preview_query($query) {
    try {
        // Execute the query using the database class
        $result = \system\database::rawQuery($query);

        if (!$result) {
            return [
                'success' => false,
                'error' => 'Query execution failed'
            ];
        }

        // Fetch all results
        $data = $result->fetchAll(\PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'data' => $data,
            'count' => count($data)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get data source info for HTMX selector
 */
function source_info($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? '';

        if (empty($data_source_id)) {
            return ''; // Return empty for no selection
        }

        // Handle "new:" prefixed selections
        if (str_starts_with($data_source_id, 'new:')) {
            $table_name = substr($data_source_id, 4);
            $available_tables = data_source_manager::get_available_tables();

            foreach ($available_tables as $table) {
                if ($table['name'] === $table_name) {
                    return '<div class="mt-2 p-3 bg-gray-50 rounded-md">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">New: ' . htmlspecialchars($table['display_name']) . '</p>
                                        <p class="text-sm text-gray-500">' . htmlspecialchars($table['description']) . '</p>
                                        <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                            <span>Table: <span class="font-mono">' . htmlspecialchars($table['name']) . '</span></span>
                                            <span>Category: <span class="capitalize">' . htmlspecialchars($table['category']) . '</span></span>
                                            <span>Rows: <span>' . number_format($table['row_count']) . '</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>';
                }
            }
            return '';
        }

        // Get existing data source
        $data_source = data_source_manager::get_data_source($data_source_id);
        if (!$data_source) {
            return '';
        }

        return '<div class="mt-2 p-3 bg-gray-50 rounded-md">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                            </svg>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">' . htmlspecialchars($data_source['name']) . '</p>
                            <p class="text-sm text-gray-500">' . htmlspecialchars($data_source['description'] ?? '') . '</p>
                            <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                <span>Table: <span class="font-mono">' . htmlspecialchars($data_source['table_name']) . '</span></span>
                                <span>Category: <span class="capitalize">' . htmlspecialchars($data_source['category'] ?? 'other') . '</span></span>
                            </div>
                        </div>
                    </div>
                </div>';

    } catch (Exception $e) {
        return '<div class="mt-2 p-3 bg-red-50 rounded-md text-red-600 text-sm">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Get preview data for HTMX data preview
 */
function preview_data($params) {
    try {
        $data_source_id = $params['data_source_id'] ?? '';
        $limit = (int)($params['limit'] ?? 5);

        if (empty($data_source_id) || str_starts_with($data_source_id, 'new:')) {
            return '<div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                        </div>
                        <div class="border rounded-md overflow-hidden">
                            <div class="p-4 text-center text-gray-500">
                                No preview available for new data sources
                            </div>
                        </div>
                    </div>';
        }

        $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => $limit]);

        if (!$result['success'] || empty($result['data'])) {
            return '<div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                            <button type="button"
                                    class="text-sm text-indigo-600 hover:text-indigo-500"
                                    hx-get="' . APP_ROOT . '/api/data_sources/preview_data"
                                    hx-vals=\'{"data_source_id": "' . htmlspecialchars($data_source_id) . '", "limit": ' . $limit . '}\'
                                    hx-target="#data_preview_container"
                                    hx-swap="innerHTML"
                                    hx-indicator="#preview_loading">
                                Refresh
                            </button>
                        </div>
                        <div class="border rounded-md overflow-hidden">
                            <div class="p-4 text-center text-gray-500">
                                ' . htmlspecialchars($result['error'] ?? 'No data available') . '
                            </div>
                        </div>
                    </div>';
        }

        $data = $result['data'];
        $columns = !empty($data) ? array_keys($data[0]) : [];

        $table_html = '';
        if (!empty($columns)) {
            $table_html = '<table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>';

            foreach ($columns as $column) {
                $table_html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">' . htmlspecialchars($column) . '</th>';
            }

            $table_html .= '</tr></thead><tbody class="bg-white divide-y divide-gray-200">';

            foreach ($data as $row) {
                $table_html .= '<tr>';
                foreach ($columns as $column) {
                    $value = $row[$column] ?? '';
                    if (is_string($value) && strlen($value) > 50) {
                        $value = substr($value, 0, 50) . '...';
                    }
                    $table_html .= '<td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">' . htmlspecialchars($value) . '</td>';
                }
                $table_html .= '</tr>';
            }

            $table_html .= '</tbody></table>';
        }

        return '<div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                        <button type="button"
                                class="text-sm text-indigo-600 hover:text-indigo-500"
                                hx-get="' . APP_ROOT . '/api/data_sources/preview_data"
                                hx-vals=\'{"data_source_id": "' . htmlspecialchars($data_source_id) . '", "limit": ' . $limit . '}\'
                                hx-target="#data_preview_container"
                                hx-swap="innerHTML"
                                hx-indicator="#preview_loading">
                            Refresh
                        </button>
                    </div>
                    <div class="border rounded-md overflow-hidden">
                        <div id="preview_loading" class="htmx-indicator p-4 text-center text-gray-500">
                            <svg class="animate-spin h-5 w-5 mx-auto mb-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading preview...
                        </div>
                        <div class="overflow-x-auto">
                            ' . $table_html . '
                        </div>
                    </div>
                </div>';

    } catch (Exception $e) {
        return '<div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                    </div>
                    <div class="border rounded-md overflow-hidden">
                        <div class="p-4 text-center text-red-500">
                            Error: ' . htmlspecialchars($e->getMessage()) . '
                        </div>
                    </div>
                </div>';
    }
}

/**
 * Create modal for new data source
 */
function create_modal($params = []) {
    try {
        $available_tables = data_source_manager::get_available_tables();

        $modal_html = '<div class="fixed inset-0 z-50 overflow-y-auto">
                        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                            </div>

                            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                                <form hx-post="' . APP_ROOT . '/api/data_sources/create_data_source_htmx"
                                      hx-target="#create_modal_container"
                                      hx-swap="innerHTML">
                                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                            Create New Data Source
                                        </h3>

                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Table</label>
                                                <select name="table_name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                                    <option value="">Select a table...</option>';

        foreach ($available_tables as $table) {
            $modal_html .= '<option value="' . htmlspecialchars($table['name']) . '">' . htmlspecialchars($table['display_name']) . ' (' . number_format($table['row_count']) . ' rows)</option>';
        }

        $modal_html .= '                        </select>
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Name</label>
                                                <input type="text"
                                                       name="name"
                                                       required
                                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                       placeholder="Enter data source name">
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700">Description</label>
                                                <textarea name="description"
                                                          rows="3"
                                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                                          placeholder="Optional description"></textarea>
                                            </div>

                                            <input type="hidden" name="category" value="other">
                                        </div>
                                    </div>

                                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                                        <button type="submit"
                                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm">
                                            Create
                                        </button>
                                        <button type="button"
                                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                                                hx-get="' . APP_ROOT . '/api/data_sources/close_modal"
                                                hx-target="#create_modal_container"
                                                hx-swap="innerHTML">
                                            Cancel
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>';

        return $modal_html;

    } catch (Exception $e) {
        return '<div class="p-4 text-red-600">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Create data source via HTMX form submission
 */
function create_data_source_htmx($params) {
    try {
        // Validate required fields
        $required_fields = ['name', 'table_name'];
        foreach ($required_fields as $field) {
            if (empty($params[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Prepare data source configuration
        $config = [
            'name' => $params['name'],
            'table_name' => $params['table_name'],
            'description' => $params['description'] ?? '',
            'category' => $params['category'] ?? 'other',
            'column_mapping' => $params['column_mapping'] ?? [],
            'filters' => $params['filters'] ?? []
        ];

        $data_source_id = data_source_manager::create_data_source($config);

        // Trigger page refresh and close modal
        header('HX-Trigger: {"dataSourceCreated": {"id": ' . $data_source_id . ', "message": "Data source created successfully"}}');

        return '<div class="text-center p-4">
                    <div class="text-green-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Success!</h3>
                    <p class="text-sm text-gray-600 mb-4">Data source created successfully</p>
                </div>';

    } catch (Exception $e) {
        return '<div class="text-center p-4">
                    <div class="text-red-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error</h3>
                    <p class="text-sm text-gray-600 mb-4">' . htmlspecialchars($e->getMessage()) . '</p>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-get="' . APP_ROOT . '/api/data_sources/close_modal"
                            hx-target="#create_modal_container"
                            hx-swap="innerHTML">
                        Close
                    </button>
                </div>';
    }
}

/**
 * Close modal - returns empty content
 */
function close_modal($params = []) {
    header('HX-Trigger: closeDataSourceModal');
    return '';
}

