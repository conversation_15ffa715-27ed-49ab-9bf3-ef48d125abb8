<?php
namespace api\data_table_config;

use system\data_table_config;
use system\data_source_manager;
use edge\Edge;
use Exception;

/**
 * Get columns from a data source for column configuration
 */
function get_data_source_columns($params) {
    try {
        $data_source_id = (int)($params['data_source_id'] ?? 0);

        if ($data_source_id <= 0) {
            return json_encode([
                'success' => false,
                'error' => 'Invalid data source ID'
            ]);
        }

        $columns = data_table_config::get_data_source_columns($data_source_id);
        $available_fields = array_column($columns, 'key');

        // Return HTML template for HTMX
        return Edge::render('data-table-column-manager-content', [
            'columns' => $columns,
            'available_fields' => $available_fields
        ]);

    } catch (Exception $e) {
        return '<div class="text-red-500 p-4">Error loading columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Save data table configuration
 */
function save_configuration($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        $data_source_id = (int)($params['data_source_id'] ?? 0);
        $column_structure = json_decode($params['column_structure'] ?? '[]', true);
        $hidden_columns = json_decode($params['hidden_columns'] ?? '[]', true);
        $settings = json_decode($params['settings'] ?? '{}', true);
        
        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }
        
        $config = [
            'data_source_id' => $data_source_id > 0 ? $data_source_id : null,
            'column_structure' => $column_structure,
            'hidden_columns' => $hidden_columns,
            'settings' => $settings
        ];
        
        $success = data_table_config::save_config($table_name, $config);
        
        if ($success) {
            // Return updated table HTML
            $callback = $params['callback'] ?? '';
            if (!empty($callback)) {
                // Include the data table API functions
                require_once __DIR__ . '/data_table.api.php';
                
                // Call the data table filter function to regenerate the table
                $table_params = array_merge($params, [
                    'table_name' => $table_name,
                    'callback' => $callback
                ]);
                
                return \api\data_table\data_table_filter($table_params);
            }
            
            return json_encode([
                'success' => true,
                'message' => 'Configuration saved successfully'
            ]);
        } else {
            throw new Exception('Failed to save configuration');
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Get data table configuration
 */
function get_configuration($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        
        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }
        
        $config = data_table_config::get_config($table_name);
        
        return json_encode([
            'success' => true,
            'config' => $config
        ]);
        
    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Apply configuration and reload table
 */
function apply_configuration($params) {
    try {
        $table_name = $params['table_name'] ?? '';
        $callback = $params['callback'] ?? '';
        
        if (empty($table_name)) {
            throw new Exception('Table name is required');
        }
        
        // Get saved configuration
        $config = data_table_config::get_config($table_name);
        
        if (!$config) {
            throw new Exception('No configuration found for table');
        }
        
        // Include the data table API functions
        require_once __DIR__ . '/data_table.api.php';
        
        // Prepare parameters for the callback
        $table_params = array_merge($params, [
            'table_name' => $table_name,
            'callback' => $callback,
            'data_source_id' => $config['data_source_id'] ?? 0
        ]);
        
        // Call the data table filter function to regenerate the table
        return \api\data_table\data_table_filter($table_params);
        
    } catch (Exception $e) {
        http_response_code(400);
        return json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>
