<?php
/**
 * Storage for delayed print_rr outputs until POOFED is defined
 */
$GLOBALS['_DELAYED_PRINT_RR'] = [];

/**
 * Flush all delayed print_rr outputs
 */
function flush_delayed_print_rr() {
    if (!empty($GLOBALS['_DELAYED_PRINT_RR'])) {
        foreach ($GLOBALS['_DELAYED_PRINT_RR'] as $output) {
            echo $output;
        }
        // Clear the array after flushing
        $GL<PERSON><PERSON>LS['_DELAYED_PRINT_RR'] = [];
    }
}


/**
 * @param string $i
 * @param $l
 * @param $co
 * @param $fl
 * @param $e
 * @param $r
 * @return string|void
 * @throws Exception
 */
function print_rr($input = null, string|null $label = null, bool|null $comment_out = true, bool|null $full = null, $throw_ex = false, $return = false, mixed $i = '', $l = null, bool $co = true, bool $fl = null, bool $e = false, bool $r = false,int $type = 0, bool $full_params = false,$max_trace_count = 2) {
   $debug = $_SESSION['debug_mode'] ?? DEBUG_MODE;
   if ( $debug || true){
       ob_start();
       $input = $input ?? $i;
       $label = $label ?? $l;
       $comment_out = $comment_out ?? $co;
       $full = $full ?? $fl;
       $return = $return ?? $r;
       $throw_exception = $throw_ex ?? $e;
       $allowed_admin = ['*************', '************'];
       // Get the user's IP address
       $user_ip = '999.999.999';
       if (isset($_SERVER['REMOTE_ADDR'])) $user_ip = $_SERVER['REMOTE_ADDR'];
       // Check if the user's IP is in the allowed IPs array
       //echo "ip: " . $user_ip  ."allowed admin:" . print_r($allowed_admin, true) . PHP_EOL;
       $eol = $co ? PHP_EOL : PHP_EOL . '<br>';
       if (empty($input)) $input = "<< empty " . gettype($input) . '>>';
      // if (is_array($allowed_admin) && in_array($user_ip, $allowed_admin)) {
           $trace = debug_backtrace();
           if ($label) $label .= ': ';
           $function_name = isset($trace[1]) && isset($trace[1]['function']) ? $trace[1]['function'] : 'global';
           $label = $label . basename($trace[0]['file']) . ' > ' . $function_name . '() ' . $trace[0]['line'];

           if (!$full && (is_string($input) || is_numeric($input))) {
               echo  '<!-- ' . $label . ": " . $input . ' -->' . $eol;
               $full = $full ?? false;
           } else {
               if ($type > 0) {
                   var_dump($input);
                   $full = $full ?? true;
                }else {
                   echo $comment_out ? $eol . '********************************************************************************************************************************************************' . $eol : '<div class="debug m-w-lg"><pre class="m-w-lg">>';
                   echo $label . $eol;
                   var_dump($input);
                   $full = $full ?? true;
               }
           }
           if ($full) {
               if ($type < 1) echo $co ? $eol . '    ---------------------------------------------------------------------------- ' . $eol : '</pre><pre class="">';
               $trace_count = 0;
               if (!empty($trace) && count($trace) > 1) {
                   while ($trace[0]['function'] == __FUNCTION__) array_shift($trace);

                       foreach ($trace as $index => $call) {
                           if ($trace_count > $max_trace_count) break;
                           echo "      <strong>Function:</strong> {$call['function']}, File: {$call['file']}, Line: {$call['line']}\n";
                           if (isset($call['args'])) {
                               echo "         <strong>Arguments:</strong> " . $eol;
                               $count = 0;
                               foreach ($call['args'] as $arg) {
                                   $arg_text = json_encode($arg);
                                   if (!$full_params && strlen($arg_text) > 100) $arg_text = substr($arg_text, 0, 100) . '...';
                                   $arg_text = str_replace(["<!--", '-->'], '', $arg_text);
                                   echo "         " . $count++ . ': ' . $arg_text . $eol;
                               }
                           }
                           $trace_count++;
                       }

                   if ($type < 1) echo $comment_out ? $eol . '----------------------------------------------------------------------------' . $eol : '</pre></div>';
               }
           }

           $output = ob_get_clean();
           $output = preg_replace('/=>\s*/',': ',$output);
           $output = str_replace(["<!--", '-->'], '', $output);
           $output = $comment_out ? $eol . '<!--' . $output: $output;
           $output = $comment_out ? $output . '-->' . $eol : $output;
           if ($return) return $output;

           // Check if POOFED is defined
           if (!defined('POOFED')) {
               // Store the output for later
               $GLOBALS['_DELAYED_PRINT_RR'][] = $output;
           } else {
               // Output immediately
               echo $output;
           }

           if ($throw_exception) throw new Exception('' . $label . ':' . $input);
     //  }
   }
}

// Logging setup
function tcs_log($message, $subject = 'main', $verbose = false){
    if (!DEBUG_MODE) return false;
    $log_file = FS_SYS_LOGS . DIRECTORY_SEPARATOR . "{$subject}_logfile.log";
    $timestamp = date("Y-m-d H:i:s");

    // Get debug backtrace info
    $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 1)[0];
    $file = basename($trace['file']);
    $line = str_pad($trace['line'] . ']', 4, ' ', STR_PAD_RIGHT);


    $message = $verbose ? print_rr(input: $message, full: true, return: true, type: 1, full_params:true) : print_r($message, true);

    $message = "[$subject] [$timestamp] [$file:$line " . preg_replace('/(\r\n|\n|\r)/','\\n',$message) . "\n";
    file_put_contents(
        $log_file,
        $message,
        FILE_APPEND
    );
    return $message;
}
function print_re($input, $note = "data: ")
{
    //$trace = debug_backtrace();
    $output = '<pre> . ' . PHP_EOL . ' ************************************** ' . PHP_EOL;
    $output .= $note . PHP_EOL;
    print_r($input);
    $output .= '
		trace: ' . PHP_EOL;
    $trace = debug_backtrace();
    foreach ($trace as $index => $call) {
        $output .= "Function: {$call['function']}, File: {$call['file']}, Line: {$call['line']}\n";
        if ($index > 0 && isset($call['args'])) {
            $output .= "Arguments: " . json_encode($call['args']) . "\n";
        }
    }
    $output .= '</pre>';
    return $output;

}


function hasVariations($id){
    $q = tep_db_query("SELECT `products_variations_id` FROM `products_variations` where `products_id` = " . $id . " limit 1;");
    return tep_db_num_rows($q) > 0;
}


function autodesk_products_getislinked($products_id)
{
    $q = tep_db_query("SELECT p2a.products_to_autodesk_catalog_id, pac.offeringName
						FROM products_to_autodesk_catalog p2a
						JOIN products_autodesk_catalog pac ON p2a.products_autodesk_catalog_id = pac.id
						where products_id = " . $products_id . "
						limit 1;");
    $qa = tep_db_fetch_array($q);
    return tep_db_num_rows($q) > 0 ? $qa['offeringName'] : false;
}

function get_autodesk_product($id)
{
    $q = tep_db_query("SELECT id, orderAction, offeringName,accessModel_description, term_description, specialProgramDiscount_code
						FROM products_autodesk_catalog
						where id = " . $id . "
						limit 1;");

    $qa = tep_db_fetch_array($q);

    $link_string = $qa['id'] . ': ' . $qa['orderAction'] . ' ' . $qa['offeringName'] . ' ' . $qa['accessModel_description'] . ' ' . $qa['term_description'] . ' ' . $qa['specialProgramDiscount_code'];

    return tep_db_num_rows($q) > 0 ? $link_string : false;
}

function clean_url_path_for_splicing($url)
{
    $url .= '/';
    $url = preg_replace('/^\//', '', $url);
    $url = preg_replace('/\/\//', '/', $url);
    return $url;
}



function tcs_handle_paths(string|array $paths){
    if (is_array($paths)) $paths = implode('/', $paths);
    return trim(str_replace('//', '/', $paths));
}

function deltree($path, $target = 'views'):bool{
    // Define allowed target paths
    switch($target){
        case 'views':
            $target_path = FS_VIEWS;
            break;
        case 'templates':
            $target_path = FS_TEMPLATES;
            break;
        case 'components':
            $target_path = FS_COMPONENTS;
            break;
        default:
            tcs_log("Security error: Invalid target path provided", 'file_delete');
           return false;
            break;
    }
    
    // Normalize paths to prevent directory traversal
    $app_root = rtrim(realpath(FS_APP_ROOT), '/') . '/';
    $sys_path = rtrim(realpath(FS_SYSTEM), '/') . '/';
    $res_path = rtrim(realpath(FS_RESOURCES ?? FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources'), '/') . '/';
    $target_path = rtrim(realpath($target_path), '/') . '/';
    $path = realpath($path);
    
    // Security checks
    if (!$path) {
        tcs_log("Security error: Invalid path provided", 'file_delete');
        return false;
    }
    
    // Check if path is empty
    if (empty($path)) {
        tcs_log("Security error: Empty path provided", 'file_delete');
        return false;
    }
    
    // Check if path is within application directory
    if (strpos($path, $app_root) !== 0) {
        tcs_log("Security error: Path outside application directory", 'file_delete');
        return false;
    }
    
    // Check if path is within target directory
    if (strpos($path, $target_path) !== 0) {
        tcs_log("Security error: Path outside target directory", 'file_delete');
        return false;
    }
    
    // Ensure path is at least one level below system or resources
    if (strpos($path, $sys_path) === 0) {
        $relative_path = substr($path, strlen($sys_path));
        if (strpos($relative_path, '/') === false) {
            tcs_log("Security error: Cannot delete top-level system directory", 'file_delete');
            return false;
        }
    }
    
    if (strpos($path, $res_path) === 0) {
        $relative_path = substr($path, strlen($res_path));
        if (strpos($relative_path, '/') === false) {
            tcs_log("Security error: Cannot delete top-level resources directory", 'file_delete');
            return false;
        }
    }
    
    try{
        $iterator = new DirectoryIterator($path);
        foreach ($iterator as $fileinfo) {
            if($fileinfo->isDot()) continue;
            if($fileinfo->isDir()){
                // Recursively call with the same target to maintain security check
                if(deltree($fileinfo->getPathname(), $target))
                    @rmdir($fileinfo->getPathname());
            }
            if($fileinfo->isFile()){
                @unlink($fileinfo->getPathname());
            }
        }
    } catch (Exception $e){
        tcs_log("Error deleting directory: " . $e->getMessage(), 'file_delete');
        return false;
    }
    return true;
}
/**
 * Global callback function for log table filtering
 * This function bridges the gap between the data table system and the namespaced function
 */
if (!function_exists('render_log_table_callback')) {
    function render_log_table_callback($criteria = []) {
        return \system\logs\render_log_table($criteria, true);
    }
}