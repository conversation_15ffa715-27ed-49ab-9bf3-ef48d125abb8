<?php
namespace edge;
use const icons\ICONS;
class edge {
    private static $renderDepth = 0;
    static int $id_count = 0;
    protected static array $base_patterns = [    ];
    protected static $secondary_patterns = []; //see compile function
    protected static $patterns = [];
    protected static array $replacements = [];
    protected static string $original_source = '';
    protected static array $use_directives = [];
    protected static array $dependencies = [
        '$loop' => [
            'key' => 'edge_loop',
            'namespace' => 'edge\\loop',
            'code' => '$loop = new Loop(0)'
        ],
        '$pagination' => [
            'key' => 'edge_pagination',
            'namespace' => 'edge\\pagination',
            'code' => '$pagination = new pagination($item_count,$first_item,$current_page_num,$items_per_page)'
        ],
        'router::' => [
            'key' => 'router',
            'namespace' => 'system\\router'
        ],
        'slot_helper::' => [
            'key' => 'slot_helper',
            'namespace' => 'edge\\slot_helper'
        ],
        'autodesk_api::' => [
            'key' => 'autodesk_api',
            'namespace' => 'autodesk_api\\autodesk_api',
        ],
        'data_table::' => [
            'key' => 'data_table',
            'namespace' => 'data_table\\data_table',
        ],
        'users::' => [
            'key' => 'users',
            'namespace' => 'system\\users',
        ],
        'database::' => [
            'key' => 'database',
            'namespace' => 'system\\database',
        ],
        'data_importer::' => [
            'key' => 'data_importer',
            'namespace' => 'system\\data_importer',
        ],
        'data_table_generator::' => [
            'key' => 'data_table_generator',
            'namespace' => 'system\\data_table_generator',
        ],
        'table_config_manager::' => [
            'key' => 'table_config_manager',
            'namespace' => 'system\\table_config_manager',
        ],
        'data_source_manager::' => [
            'key' => 'data_source_manager',
            'namespace' => 'system\\data_source_manager',
        ],
        'hilt::' => [
            'key' => 'hilt',
            'namespace' => 'system\\hilt',
        ],
        'ICONS' => [
            'key' => 'icons',
            'namespace' => 'const icons\\ICONS',
        ],
        'icon(' => [
            'key' => 'icons',
            'namespace' => 'function icons\\icon',
        ],
        'icon_micro(' => [
            'key' => 'icons',
            'namespace' => 'function icons\\icon_micro'
        ]
    ];

    /**
     * @param string $edge_name
     * @param array $data
     * @return false|string
     */
    public static function render(string $edge_name, array|null $data = [], int|null $tag_content = 0, string $content = ''): string {
       // tcs_log("Starting: $edge_name'", 'edge');
        //if (DEBUG_MODE) print_rr($data, "data for $edge_name");
        self::$renderDepth++;
        // error_log("Debug: Edge::render depth {$self::renderDepth} for {$edge_name}");
        if (self::$renderDepth > 10) {
            error_log("Warning: Maximum render depth exceeded for {$edge_name}");
            self::$renderDepth--;
            return "Error: Maximum template depth exceeded";
        }

        $system_edge_path = 'system/components/edges/' . $edge_name . '.edge.php';
        $edge_path = 'resources/components/edges/' . $edge_name . '.edge.php';
        $blade_path = 'resources/components/edges/' . $edge_name . '.blade.php';
        if(!is_dir( FS_TEMP . DS . 'edge')) {
            mkdir(FS_TEMP . DS . 'edge', 0755, true);
        }

        $compiled_path = FS_TEMP . DS . 'edge' . DS . $edge_name . '.edge.php';

        if (file_exists($edge_path)) {
            $file_path = $edge_path;
        } elseif (file_exists($system_edge_path))  {
            $file_path = $system_edge_path;
        }elseif (file_exists($blade_path)) {
            $file_path = $blade_path;
        } else {
            return die("Edge file not found: {$edge_path}");
        }
        $data['tag_content'] = $tag_content;

        // If content is provided, make it available to the component
        if (!empty($content)) {
            $data['content'] = $content;
        }

        $data['edge_manifest']['name'] = $edge_name;
        $data['edge_manifest']['file_path'] = $file_path;
        // Check if cached version exists

        if (!file_exists($compiled_path) || filemtime($file_path) > filemtime($compiled_path) || filemtime(__FILE__) > filemtime($compiled_path)) {
            tcs_log("compiling_path: '$file_path' to '$compiled_path'");
            $uncompiled = file_get_contents($file_path);
            $compiled = self::compile($uncompiled, $edge_name,$data['edge_manifest']);
            file_put_contents($compiled_path, $compiled);
        }
        self::$renderDepth--;
       // print_rr(input:tcs_log("starting rendering: $compiled_path'", 'edge'),full:true);

        return self::phprender($data, $compiled_path);
    }

    public static function phprender($edge_data, $compiled_path): false|string {
        ob_start();
        extract($edge_data);
     //   print_rr(tcs_log("Including: $compiled_path'", 'edge'));
        include($compiled_path);
     //   print_rr(tcs_log("Complete $compiled_path", 'edge'));
        return ob_get_clean();
    }

    /**
     * Renders a view file using Edge template syntax
     *
     * @param string $view_path Path to the view file
     * @param array $data Data to pass to the view
     * @return string Rendered view content
     */
    public static function renderView(string $view_path, array $data = []): string {
        // Generate a unique name for the compiled view
        $view_hash = md5($view_path);
        $compiled_dir = FS_TEMP . DS . 'views' . DS;

        // Create the compiled directory if it doesn't exist
        if (!is_dir($compiled_dir)) {
            mkdir($compiled_dir, 0755, true);
        }

        // print_rr($view_path, "view_pathtcs");
        $view_array = explode(DS,$view_path);
        $view_filename = explode('.',array_pop($view_array))[0];
        $compiled_path = $compiled_dir . $view_filename . '_' . $view_hash . '.comp.php';

        // Check if we need to compile the view
        if (!file_exists($compiled_path) || filemtime($view_path) > filemtime($compiled_path) || filemtime(__FILE__) > filemtime($compiled_path)) {
            $uncompiled = file_get_contents($view_path);
            $compiled = self::compile($uncompiled, 'view_' . $view_hash);
            file_put_contents($compiled_path, $compiled);
        }
        // Render the compiled view
        return self::phprender($data, $compiled_path);
    }

    public static function id_count(): int {
        if (!isset($_SESSION['id_count'])) $_SESSION['id_count'] = 0;
        return ++$_SESSION['id_count'];
    }

    public static function compile($source, $edge_name,$manifest = []): string    {
        // Store the original source for tag_content detection
        self::$original_source = $source;
        self::$base_patterns = [
            // Regex patterns

            'props_declaration' => '/@props\s*\(\s*\[(.*?)\]\s*\)/s',
            'use_directive' => '/@use\s+([a-zA-Z_\\\\][a-zA-Z0-9_\\\\]*)\s*;?/',
            'variable' => '/(?<!\@)(?:\{\{|\{!!)\s*(.*?)\s*(?:!!\}|\}\})/s',

            'edge_tag_open' => '/<x-([\w-]+|{{ [^}]* }})(.*?)(?:(\/>)|(?<!-)(?<!=)>)/s',
            'edge_tag_close' => '/<\/x-([\w-]+|{{ [^}]* }})>/s',
            'slot_tag_open' => '/<x-slot\s+name=["\']([^"\']+)["\'][^>]*>/s',
            'slot_tag_close' => '/<\/x-slot>/s',
            'component_tag_open' => '/@component\(([^)]+)\)/s',
            'component_tag_content' => '/@component\(.+?\)(.+?)@endcomponent/s',
            'component_tag_close' => '/@endcomponent/',
            'attribute' => '/([\w\-:@_]+)=(?|(?:([\'\"])([^{}]*?)\2)|([^{} ]*?)[ >]|([\'\"])([^{} ]*?(?>{{ )[^}]*(?: }})[^{} ]*?)\2)/s',
            'comments' => '/(<!|{{)--.*?--(>|}})/s',
            'foreach_loop' => '/@foreach\s*\((.+?)( as [^)]+)\)/',
            'if_statement' => '/@if\s*\((.*)\)/',
            'switch_statement' => '/@switch\s*\((.*)\)\s*@case\s*\(([^)]*)\)/s',
            'switch_case_statement' => '/@case\s*\((.*)\)/',
            'switch_default' => '@default',
            'switch_end' => '@endswitch',
            'icon' => '/@icon([^(]*)\((([^)]*))\)/',
            'elseif_statement' => '/@elseif\s*\((.*)\)/',
            'php_include' => '/@(include|require)(_once)?((?:[\s\(])*([a-zA-Z$_-]*)[\)]?)/',
            'util_function' => '/@(print_rr|log)\((.*)\)/s',
            'view' => '/@view\s*([a-zA-Z$_-]*)/',
            'db_query' => '/@db\s*\(\s*[\'"]([^\'"]+)[\'"]\s*(?:,\s*(.+?))?\s*\)/',
            'db_table' => '/@table\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)/',
            'db_get' => '/@get\s*\(\s*(.+?)\s*\)/',
            //  'iconsConst' => '/[^\\](ICONS\[[\'|\"][^\]]+?[\'|\"]\])/',

            // String patterns
            'php_tag_open' => '@php',
            'php_tag_close' => '@endphp',
            'else' => '@else',
            'endif' => '@endif',
            'break' => '@break',
            'endforeach' => '@endforeach',
            'tag_strip' => ['\<\?php ', '<?= ', ' ?>'],
            'php_consecutive_conditionals_tags' => '/([:}])\s*\?>(\s*)<\?php/',
            'php_consecutive_conditionals_echo_tags' => '/([:}])\?>(\s*)<\?=/',
            'php_consecutive_tags' => '/\?>(\s*)<\?php/',
            'php_consecutive_echo_tags' => '/\?>(\s*)<\?=/',
            'html_minify' => '/\>\s*\</s',
            'html_minify_newLines' => '/\s*\R\s*/',
            //section tags

            'tag_content' => '/(?>\{\{\=\s*tag_content\s*(?>\=\}\}))/',
            'escape_cleanup' => '/@({{|}})/',
        ];
       // print_rr(self::$base_patterns, 'base_patterns');
        self::$secondary_patterns = [
            'edge_tag_content' => '/' . substr(self::$base_patterns['edge_tag_open'],1,-2) . '.+?' . substr(self::$base_patterns['edge_tag_close'],1,-2) . '/s',
            'bound_variable' => '/(?<=[\'"])' .  substr(self::$base_patterns['variable'],1,-2) . '/',
            'bound_sub_variable' => '/(?<![\'"])' . substr(self::$base_patterns['variable'],1,-2) . '/'
        ];
        // print_rr(self::$secondary_patterns, 'secondary_patterns');
        self::$patterns = array_merge(self::$base_patterns,self::$secondary_patterns);
        self::$replacements = [
            'variable' => '<?= $1 ?>',
            'variable_notag' => '$1$2',
            'foreach_loop' => '<?php if (is_countable($1)) { $loop = new loop(count($1),$loop->depth+1,$loop); foreach($1$2): ?>',
            'foreach_end' => '<?php $loop->update(); endforeach; $loop = $loop->parent; } ?>',
            'if_end' => '<?php endif ?>',
            'switch_end' => '<?php endswitch ?>',
            'switch_default' => '<?php default: ?>',
            'else' => '<?php else: ?>',
            'break' => '<?php break; ?>',
            'component_tag' => '<?php edge::render($1); ?>',
            'icon' => '<?= icon$1($2) ?>',
            'blade_comments' => '',
            'php_tag_open' => '<?php',
            'php_tag_close' => '?>',
            'php_include' => '<?php $1$2$3 ?>',
            'view' => '<?php router::get_route($1) ?>',
            'db_query' => '<?php $db_result = database::query()->raw("$1"); ?>',
            'db_table' => '<?php $db_table = database::table("$1"); ?>',
            'db_get' => '<?php $db_data = $1->get(); ?>',
            'html_minify' => '><',
            'html_minify_newLines' => ' ',
            'php_consecutive_tags' => ';$1',
            'php_consecutive_echo_tags' => ';$1 echo',
            'php_consecutive_conditional_tags' => '$1$2',
            'php_consecutive_conditional_echo_tags' => '$1$2 echo',
            'util_function' => '<?php $1($2); ?>',
            //section tags
            //  'iconsConst' =>  '\\icons\\$1',
            'tag_content' => '<?php } if($tag_content == 0 || $tag_content == 2 ){ ?>',
            'escape_cleanup' => '$1',
        ];
        $pipeline = [
            'handle_tag_sections',
            'process_use_directives',
            'insert_view',
            'remove_comments',
            'preprocess_slots',
            'compile_edge_tags',
            'compile_variables',
            'compile_conditional_statements',
            'compile_statements',
            'compile_loops',
            'compile_php_tags',
            'compile_database_queries',
            'compile_icons',
            'compile_functions',
            'compile_escape_cleanup',
            'compile_finalize'
        ];

        return array_reduce($pipeline, function ($carry, $method) use ($edge_name,$manifest) {
            return self::$method($carry, $edge_name,$manifest);
        }, $source);
    }

    protected static function compile_escape_cleanup($source) {
        return preg_replace(self::$patterns['escape_cleanup'], self::$replacements['escape_cleanup'], $source);
    }
    protected static function compile_functions($source) {
        return  preg_replace_callback(self::$patterns['util_function'], function ($matches) {
            $startPos = strpos($matches[0], '(');
            $condition = trim(self::parse_bracketed_pattern($matches[0], $startPos),"()");
            $theRest = trim(substr($matches[0], $startPos + strlen($condition) + 2));
            return "<?= " . $matches[1] . "(" . $condition . "); ?> " . $theRest . ';';
        }, $source);

    }
    protected static function debigulateHTML($source) {
          return preg_replace(self::$patterns['html_minify_newLines'], self::$replacements['html_minify_newLines'],
          preg_replace(self::$patterns['html_minify'], self::$replacements['html_minify'], $source)
      );
    }

    protected static function process_use_directives($source, $edge_name, $manifest): string {
        // Reset use directives for this compilation
        self::$use_directives = [];

        // Find all @use directives and extract namespaces
        $source = preg_replace_callback(
            self::$patterns['use_directive'],
            function ($matches) {
                $namespace = trim($matches[1]);
                // Store the namespace for later injection
                self::$use_directives[] = $namespace;
                // Remove the @use directive from the source
                return '';
            },
            $source
        );

        return $source;
    }

    protected static function insert_functionality(string $source, $edge_name): array {

        $edge_name = str_replace('-', '_', $edge_name);
        $namespace_inject = $include_inject = $function_inject = $code_inject = $class_inject = [];
        // Define an associative array of dependencies to check for

        // Process each dependency
        foreach (self::$dependencies as $search => $config) {
            $found = strpos($source, $search);
            if ($found !== false) {
                if (isset($config['namespace'])) {
                    $key = $config['key'] . crc32($config['namespace']);
                    $namespace_inject[$key] = $config['namespace'];
                }
                if (isset($config['class'])) {
                    $class_inject[$config['class']] = $config['class'];
                }
                if (isset($config['code'])) {
                    $key = $config['key'] . crc32($config['code']);
                    $code_inject[$key] = $config['code'];
                }
            }
        }

        $namespace_inject_string = "namespace edgeTemplate\\{$edge_name};use edge\\Edge;";

        // Add namespaces from @use directives
        if (!empty(self::$use_directives)) {
            $namespace_inject_string .= 'use ' . implode(';use ', self::$use_directives) . ';';
        }

        // Add namespaces from dependencies
        if (!empty($namespace_inject)) {
            $namespace_inject_string .= 'use ' . implode(';use ', $namespace_inject) . ';';
        }

        $include_inject_string = '';
        foreach($include_inject as $include) {
            $include_inject_string .= "require_once(FS_CLASSES . DS . '{$include}.class.php');";
        }
        $function_inject_string = '';
        foreach($function_inject as $include) {
            $function_inject_string .= "require_once(FS_FUNCTIONS . DS . '{$include}.fn.php');";
        }
        $class_inject_string = '';
        foreach($class_inject as $include) {
            $class_inject_string .= "require_once(FS_CLASSES . DS . '{$include}.class.php');";
        }
        $code_inject_string = implode(';',$code_inject) . ';';

        $inject = [
            'namespace' => $namespace_inject_string,
            'class' => $class_inject_string,
            'include' => $include_inject_string,
            'function' => $function_inject_string,
            'code' => $code_inject_string,
            'tag_content_start' => '/* print_rr($tag_content,"tag_content_start"); */ if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){',
            'tag_content_end' => '}'
        ];
        // print_rr($inject, "inject for $edge_name");
        return $inject;
    }

    protected static function insert_view(string $source): string  {

        $source = preg_replace(self::$patterns['view'], self::$replacements['view'], $source);
        return $source;
    }

    protected static function compile_finalize($source, $edge_name, $manifest): string    {
        $inject = self::insert_functionality($source, $edge_name);

        // Check if the original source (before any processing) had tag_content
        // For dynamically compiled templates or templates that don't exist as files,
        // fall back to checking the stored original source
        $template_path = self::getTemplatePath($edge_name);
        if ($template_path && file_exists($template_path)) {
            $original_source = file_get_contents($template_path);
            $has_tag_content = preg_match(self::$patterns['tag_content'], $original_source);
        } else {
            // Fallback: check the stored original source (before any processing)
            $has_tag_content = preg_match(self::$patterns['tag_content'], self::$original_source);
        }

        $tag_content_start = $has_tag_content ? $inject['tag_content_start'] : '';
        $tag_content_end = $has_tag_content ? $inject['tag_content_end'] : '';

        if (preg_match(self::$patterns['props_declaration'], $source, $m)) {
            $props_array_string = trim($m[1]);
            $props_array_string = preg_replace('/\/\/.*/', '', $props_array_string);
            $props_array_string = "['edge_manifest' => " . var_export($manifest, true) . "," . $props_array_string . PHP_EOL . "]";
            $source = str_replace($m[0], "<?php extract(Edge::pp($props_array_string,\$edge_data));{$inject['class']}{$inject['include']}{$inject['code']}{$inject['function']}{$tag_content_start}?>", $source);
        } else {
            $source = "<?php extract(['edge_manifest' => " . var_export($manifest, true) . "]);" . $inject['class']  .  $inject['include']  .  $inject['code']  .  $inject['function'] . $tag_content_start . ' ?>' . $source;
        }
        $end_php = $tag_content_end ? '<?php ' . $tag_content_end . ' ?>' : '';
        return '<?php ' .$inject['namespace'] . '?>' . $source . $end_php;
    }

    protected static function remove_comments($source)
    {
        return preg_replace(self::$patterns['comments'], '', $source);
    }

    protected static function compile_variables($source)
    {
        return preg_replace(self::$patterns['variable'], self::$replacements['variable'], $source);
    }

    protected static function compile_conditional_statements($source): string
    {
        $source = preg_replace_callback(self::$patterns['elseif_statement'], function ($matches) {
            $startPos = strpos($matches[0], '(');
            $condition = self::parse_bracketed_pattern($matches[0], $startPos);
            return "<?php elseif{$condition}: ?>";
        }, $source);
        $source = preg_replace_callback(self::$patterns['if_statement'], function ($matches) {
            $startPos = strpos($matches[0], '(');
            $condition = self::parse_bracketed_pattern($matches[0], $startPos);
            return "<?php if{$condition}: ?>";
        }, $source);
        $source = preg_replace_callback(self::$patterns['switch_statement'], function ($matches) {
            $startPos1 = strpos($matches[0], '(');
            $condition = self::parse_bracketed_pattern($matches[0], $startPos1);
            $startPos2 = strpos($matches[0], '(', $startPos + strlen($condition) + 1);
            $condition_case = self::parse_bracketed_pattern($matches[0], $startPos2);
            return "<?php switch{$condition}: case {$condition_case}:  ?>";
        }, $source);
        $source = preg_replace_callback(self::$patterns['switch_case_statement'], function ($matches) {
            $startPos = strpos($matches[0], '(');
            $condition = self::parse_bracketed_pattern($matches[0], $startPos);
            $condition = str_replace(['(',')'], '', $condition);
            return "<?php case {$condition}: ?>";
        }, $source);
        return $source;
    }

    protected static function compile_statements($source): string
    {
        $source = str_replace(self::$patterns['else'], self::$replacements['else'], $source);
        $source = str_replace(self::$patterns['endif'], self::$replacements['if_end'], $source);
        $source = str_replace(self::$patterns['switch_default'], self::$replacements['switch_default'], $source);
        $source = str_replace(self::$patterns['switch_end'], self::$replacements['switch_end'], $source);
        $source = str_replace(self::$patterns['break'], self::$replacements['break'], $source);
        return $source;
    }

    protected static function compile_loops($source): string
    {
        $source = preg_replace(self::$patterns['foreach_loop'], self::$replacements['foreach_loop'], $source);
        $source = str_replace(self::$patterns['endforeach'], self::$replacements['foreach_end'], $source);
        return $source;
    }

    protected static function compile_php_tags($source): string
    {
        $source = preg_replace(self::$patterns['php_include'], self::$replacements['php_include'], $source);
        $source = str_replace(self::$patterns['php_tag_open'], self::$replacements['php_tag_open'], $source);
        $source = str_replace(self::$patterns['php_tag_close'], self::$replacements['php_tag_close'], $source);
        return $source;
    }

    /**
     * Compile database query directives
     *
     * @param string $source Template source
     * @return string Compiled source
     */
    protected static function compile_database_queries($source): string
    {
        $source = preg_replace(self::$patterns['db_query'], self::$replacements['db_query'], $source);
        $source = preg_replace(self::$patterns['db_table'], self::$replacements['db_table'], $source);
        $source = preg_replace(self::$patterns['db_get'], self::$replacements['db_get'], $source);
        return $source;
    }

protected
static function compile_icons( $source ): string {
        return preg_replace(self::$patterns['icon'], self::$replacements['icon'], $source);
}

protected static function compile_edge_tags($source,$edge_name) {
    // Initialize missing variables
    $section_string = '';
    $simpleTags = false;

    // Use the new parser-based approach for component tags
    $source = self::parseComponentTags($source);

    // Handle any remaining self-closing tags with regex (only self-closing ones)
    $source = preg_replace_callback(
        '/<x-([\w-]+|{{ [^}]* }})(.*?)(\/>)/s',
        function ($matches) {
            $componentName = $matches[1];
            if (str_starts_with($componentName, '{{')) {
                $componentName = preg_replace(self::$patterns['variable'], self::$replacements['variable_notag'], $componentName);
            } else {
                $componentName = "'{$componentName}'";
            }
            $attributes = $matches[2];

            $props = self::parseAttributes($attributes);
            $propsString = is_array($props) ? implode(', ', $props): $props;

            // Self-closing tags don't have content
            return "<?= Edge::render({$componentName}, [" . $propsString . "]) ?>";
        },
        $source);


    $source = preg_replace_callback(
        self::$patterns['component_tag_open'],
        function ($matches) use ($section_string) {
            $attributes = $matches[1];
            return "<?= Edge::render({$attributes}{$section_string}) ?>";
        },
        $source);

    if ($simpleTags) return preg_replace(self::$patterns['edge_tag_close'], '', $source);
    $source = preg_replace_callback(
        self::$patterns['edge_tag_close'],
        function ($matches) {
            $componentName = $matches[1];
            if (str_starts_with($componentName, '{{')) {
                $componentName = preg_replace(self::$patterns['variable'], self::$replacements['variable_notag'], $componentName);
            } else {
                $componentName = "'{$componentName}'";
            }
            return "<?= Edge::render({$componentName},null,2) ?>";
        },
        $source
    );
    return preg_replace_callback(
        self::$patterns['component_tag_close'],
        function ($matches) {
            $componentName = $matches[1];
            return "<?= Edge::render('{$componentName}',null,2) ?>";
        },
        $source
    );
}

protected static function handle_tag_sections($source){
    // Only process tag_content if the pattern actually exists in the source
    if (preg_match(self::$patterns['tag_content'], $source)) {
        return preg_replace(self::$patterns['tag_content'], self::$replacements['tag_content'], $source);
    }
    return $source;
}

protected static function getTemplatePath($edge_name): ?string {
    $system_edge_path = 'system/components/edges/' . $edge_name . '.edge.php';
    $edge_path = 'resources/components/edges/' . $edge_name . '.edge.php';
    $blade_path = 'resources/components/edges/' . $edge_name . '.blade.php';

    if (file_exists($edge_path)) {
        return $edge_path;
    } elseif (file_exists($system_edge_path)) {
        return $system_edge_path;
    } elseif (file_exists($blade_path)) {
        return $blade_path;
    }

    return null; // Template file not found
}

/**
 * Parse component tags with proper nesting support
 * This replaces regex-based parsing for nested components
 */
protected static function parseComponentTags($source) {
    $result = '';
    $pos = 0;
    $length = strlen($source);

    while ($pos < $length) {
        // Find the next component tag
        $tagStart = strpos($source, '<x-', $pos);

        if ($tagStart === false) {
            // No more component tags, append the rest
            $result .= substr($source, $pos);
            break;
        }

        // Append content before the tag
        $result .= substr($source, $pos, $tagStart - $pos);

        // Parse this component tag
        $tagInfo = self::parseComponentTag($source, $tagStart);

        if ($tagInfo === false) {
            // Not a valid component tag, skip this character
            $result .= $source[$tagStart];
            $pos = $tagStart + 1;
            continue;
        }

        // Replace the component tag with rendered PHP
        $result .= $tagInfo['replacement'];
        $pos = $tagInfo['endPos'];
    }

    return $result;
}

/**
 * Parse a single component tag with its content
 * Returns array with 'replacement' and 'endPos' or false if not a valid component
 */
protected static function parseComponentTag($source, $startPos) {
    $length = strlen($source);
    $pos = $startPos;

    // Find the end of the opening tag more carefully
    $tagEnd = self::findTagEnd($source, $pos);
    if ($tagEnd === false) {
        return false;
    }

    $openingTag = substr($source, $pos, $tagEnd - $pos + 1);

    // Check if it's self-closing
    if (str_ends_with(trim($openingTag), '/>')) {
        // Self-closing tag, handle with existing regex logic
        return false; // Let regex handle it
    }

    // Extract component name and attributes (handle multi-line tags)
    if (!preg_match('/<x-([\w-]+)(.*?)>/s', $openingTag, $matches)) {
        return false;
    }

    $componentName = $matches[1];
    $attributes = $matches[2];

    // Find the matching closing tag
    $contentStart = $tagEnd + 1;
    $contentEnd = self::findMatchingClosingTag($source, $contentStart, $componentName);

    if ($contentEnd === false) {
        return false; // No matching closing tag found
    }

    // Extract the content between tags
    $content = substr($source, $contentStart, $contentEnd - $contentStart);

    // Process component name for PHP
    if (str_starts_with($componentName, '{{')) {
        $componentName = preg_replace(self::$patterns['variable'], self::$replacements['variable_notag'], $componentName);
    } else {
        $componentName = "'{$componentName}'";
    }

    // Parse attributes
    $props = self::parseAttributes($attributes);
    $propsString = is_array($props) ? implode(', ', $props) : $props;

    // If content is empty or just whitespace, treat as regular component
    if (trim($content) === '') {
        $replacement = "<?= Edge::render({$componentName}, [" . $propsString . "]) ?>";
    } else {
        // Component has actual content, use inline method
        $replacement = self::inlineComponentWithContent($componentName, $propsString, $content);
    }

    // Find the actual end position (after closing tag)
    $closingTagEnd = strpos($source, '>', $contentEnd) + 1;

    return [
        'replacement' => $replacement,
        'endPos' => $closingTagEnd
    ];
}

/**
 * Find the matching closing tag for a component, handling nesting
 */
protected static function findMatchingClosingTag($source, $startPos, $componentName) {
    $length = strlen($source);
    $pos = $startPos;
    $depth = 1; // We're already inside one tag

    $openPattern = "<x-{$componentName}";
    $closePattern = "</x-{$componentName}>";

    while ($pos < $length && $depth > 0) {
        // Find the next occurrence of either opening or closing tag
        $nextOpen = strpos($source, $openPattern, $pos);
        $nextClose = strpos($source, $closePattern, $pos);

        // If no closing tag found, return false
        if ($nextClose === false) {
            return false;
        }

        // If there's an opening tag before the closing tag, increase depth
        if ($nextOpen !== false && $nextOpen < $nextClose) {
            // Make sure it's a complete opening tag (not just a substring)
            $tagEnd = strpos($source, '>', $nextOpen);
            if ($tagEnd !== false) {
                $tagContent = substr($source, $nextOpen, $tagEnd - $nextOpen + 1);
                // Check if it's actually an opening tag (not self-closing)
                if (!str_ends_with(trim($tagContent), '/>')) {
                    $depth++;
                }
            }
            $pos = $nextOpen + strlen($openPattern);
        } else {
            // Found a closing tag
            $depth--;
            if ($depth === 0) {
                return $nextClose; // Found the matching closing tag
            }
            $pos = $nextClose + strlen($closePattern);
        }
    }

    return false; // No matching closing tag found
}

/**
 * Find the end of an opening tag, properly handling quoted attributes
 */
protected static function findTagEnd($source, $startPos) {
    $length = strlen($source);
    $pos = $startPos;
    $inQuotes = false;
    $quoteChar = '';

    while ($pos < $length) {
        $char = $source[$pos];

        if (!$inQuotes) {
            if ($char === '"' || $char === "'") {
                $inQuotes = true;
                $quoteChar = $char;
            } elseif ($char === '>') {
                return $pos; // Found the end of the tag
            }
        } else {
            if ($char === $quoteChar) {
                // Check if it's escaped
                $backslashCount = 0;
                $checkPos = $pos - 1;
                while ($checkPos >= 0 && $source[$checkPos] === '\\') {
                    $backslashCount++;
                    $checkPos--;
                }
                // If even number of backslashes (including 0), the quote is not escaped
                if ($backslashCount % 2 === 0) {
                    $inQuotes = false;
                    $quoteChar = '';
                }
            }
        }

        $pos++;
    }

    return false; // No closing > found
}

/**
 * Inline a component with its content by replacing {{= tag_content =}} placeholder
 */
protected static function inlineComponentWithContent($componentName, $propsString, $content) {
    // Get the component template source and default props
    $componentData = self::getComponentTemplateData($componentName);

    if ($componentData === false) {
        // Fallback to regular rendering if template not found
        return "<?= Edge::render({$componentName}, [" . $propsString . "], 0, " . var_export($content, true) . ") ?>";
    }

    // Replace {{= tag_content =}} with the actual content
    $processedTemplate = str_replace('{{= tag_content =}}', $content, $componentData['template']);

    // Remove {{ $content }} from the template since we're using tag_content instead
    // This prevents the "Array" issue when $content prop is an array
    $processedTemplate = preg_replace('/\{\{\s*\$content\s*\}\}/', '', $processedTemplate);

    // Merge default props with passed props, but preserve existing variable scope
    $defaultPropsString = $componentData['defaultProps'];

    // Create inline PHP code that renders the processed template
    // Use extract with EXTR_SKIP to preserve existing variables
    return "<?php " .
           "\$component_props = array_merge(" . $defaultPropsString . ", [" . $propsString . "]); " .
           "extract(\$component_props, EXTR_SKIP); " .
           "?>" . $processedTemplate;
}

/**
 * Get the template data for a component including default props
 */
protected static function getComponentTemplateData($componentName) {
    // Remove quotes from component name if present
    $componentName = trim($componentName, "'\"");

    $system_edge_path = 'system/components/edges/' . $componentName . '.edge.php';
    $edge_path = 'resources/components/edges/' . $componentName . '.edge.php';

    if (file_exists($edge_path)) {
        $file_path = $edge_path;
    } elseif (file_exists($system_edge_path)) {
        $file_path = $system_edge_path;
    } else {
        return false;
    }

    $template = file_get_contents($file_path);

    // Extract @props declaration
    $defaultProps = '[]';
    if (preg_match('/@props\s*\(\s*\[(.*?)\]\s*\)/s', $template, $matches)) {
        $defaultProps = '[' . $matches[1] . ']';
    }

    // Remove @props declaration and @php blocks for inline processing
    $template = preg_replace('/@props\s*\(\s*\[.*?\]\s*\)/s', '', $template);
    $template = preg_replace('/@php.*?@endphp/s', '', $template);

    return [
        'template' => $template,
        'defaultProps' => $defaultProps
    ];
}

/**
 * Preprocess slots before component compilation
 * This prevents <x-slot> tags from being treated as components
 */
protected static function preprocess_slots($source) {
    // Store slot content in a temporary format that won't be processed as components
    $source = preg_replace_callback(
        '/<x-slot\s+name=["\']([^"\']+)["\'][^>]*>(.*?)<\/x-slot>/s',
        function($matches) {
            $slotName = $matches[1];
            $slotContent = $matches[2];
            // Use a special marker that will be processed later
            return "<!--SLOT:{$slotName}-->{$slotContent}<!--/SLOT:{$slotName}-->";
        },
        $source
    );

    return $source;
}

/**
 * Process slots within component content
 * Extracts named slots and default slot content
 */
protected static function processSlots($content) {
    $slots = ['slot' => ''];

    // Extract named slots using the preprocessed slot markers
    $content = preg_replace_callback(
        '/<!--SLOT:([^-]+)-->(.*?)<!--\/SLOT:\1-->/s',
        function($matches) use (&$slots) {
            $slotName = $matches[1];
            $slotContent = trim($matches[2]);
            $slots[$slotName] = $slotContent;
            return ''; // Remove slot from content
        },
        $content
    );

    // Remaining content becomes the default slot
    $slots['slot'] = trim($content);

    return $slots;
}


public static function parse_tables($schema, $fields = []) {
    foreach ($schema as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $fields = self::parse_tables($value['content'], $fields);
            } elseif (isset($value['value'])) {
                $fields[$key] = $key;
            }
        }
    }
    return $fields;
}

public static function parse_layout($layout, $data) {
    foreach ($layout as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $layout[$key]['content'] = self::parse_layout($value['content'], $data);
            } elseif (isset($value['value'])) {
                $layout[$key]['value'] = $data[$key] ?? '';
            }
        }
    }
    return $layout;
}

protected static function parseAttributes($attributeString){
    $props = [];
    preg_match_all(self::$patterns['attribute'], $attributeString, $attrMatches, PREG_SET_ORDER);

    foreach ($attrMatches as $attr) {
        $key = $attr[1];
        $rawValue = $attr[3];
        if ($key == 'x-edge-data') return $rawValue;
        // Handle bound values (e.g., :options="...")
        if (str_starts_with($key, ':') && !str_starts_with($key,':::')) {
            $key = substr($key, 1);
            // Check if the value contains template variables
            if (preg_match(self::$patterns['bound_variable'], $rawValue)) {
                $value = preg_replace(self::$patterns['bound_variable'], '$1', $rawValue);
                $props[] = "'$key' => $value";
            } elseif(preg_match(self::$patterns['bound_sub_variable'], $rawValue)) {
                    $value =  preg_replace(self::$patterns['bound_sub_variable'], '" . $1 . "', $rawValue);
                    $props[] = "'$key' => \"$value\"";
            } else {
                // If no template variables, treat as direct PHP expression
                $value = $rawValue;
                $props[] = "'$key' => $value";
            }
        } else {
            if (str_starts_with($key, ':::'))  $key = substr($key, 2);
            // Regular attributes - preserve template variables
            if (preg_match(self::$patterns['variable'], $rawValue)) {
                $value = preg_replace(self::$patterns['variable'], '" . $1 . "', $rawValue);
                $props[] = "\"$key\" => \"$value\"";
            } else {
                // Regular string value
                $rawValue = preg_replace('/([$"\'])/', '\\\$1', $rawValue);
                $props[] = "\"$key\" => \"$rawValue\"";
            }
        }
    }
        return $props;
    }


public  static function parse_bracketed_pattern($content, $start): string {
    $stack = [];
    $result = '';
    $inside = false;
    for ($i = $start; $i < strlen($content); $i++) {
        $char = $content[$i];
        if ($char === '(') {
            if ($inside) {
                $stack[] = $char;
            } else {
                $inside = true;
            }
            $result .= $char;
        } elseif ($char === ')') {
            if (!empty($stack)) {
                array_pop($stack);
                $result .= $char;
            } elseif ($inside) {
                $result .= $char;
                break;
            }
        } elseif ($inside) {
            $result .= $char;
        }
    }
    return $result;
}


public
static function pp($defaultProps, $props = [], $debug = false) {
    return process_props($defaultProps, $props, $debug);
}

public
static function process_props($defaultProps, $props = [], $debug = false) {
    $extra_attributes = '';
    if ($debug) print_rr($defaultProps, "defaultProps");
    foreach ($props as $key => $prop) {
        if ($debug) print_rr("is $key in defaultProps", "default props");
//        if (($key == 'icon' || $key == 'edge-icon') && key_exists('edge-icon', $defaultProps)) {
//            $props['icon'] = ICONS[$prop];
//            continue;
//        }
        if (isset($defaultProps[$key])) continue;
        if (is_array($prop)) {
            $extra_attributes .= " {$key}='" . json_encode($prop) . "'";
        } else {
            if (is_int($key) || is_float($key) || is_bool($key)) {
                $extra_attributes .= " {$key}=$prop";
            } else {
                $extra_attributes .= " {$key}='{$prop}'";
            }
        }
    }
    $props = array_merge($defaultProps, $props);
    $props['extra_attributes'] = $extra_attributes;
    if ($debug) print_rr($props, "template props");
    //   print_rr($props, 'proppage');
    return $props;
}


/**
 * Merges multiple strings of Tailwind CSS classes, filters duplicates,
 * and overwrites conflicting classes, with the last string taking precedence.
 *
 * @param string ...$classStrings Multiple strings of Tailwind CSS classes.
 *
 * @return string A single string containing the merged and processed
 *                Tailwind CSS classes.
 */
public
static function merge_tailwind_classes(string ...$classStrings): string{
    $classes = [];

    foreach ($classStrings as $classString) {
        $classArray = preg_split('/\s+/', trim($classString));

        foreach ($classArray as $class) {
            if (empty($class)) {
                continue; // Skip empty classes
            }
            // Extract the base class name (e.g., 'bg' from 'bg-red-500')
            preg_match('/^([a-zA-Z]+(?:-[a-zA-Z]+)*)/', $class, $matches);
            $baseClass = $matches[1] ?? $class; // Use the whole class if no match

            $classes[$baseClass] = $class; // Overwrite if the base class exists
        }
    }

    return implode(' ', array_values($classes));
}

public
static function column_filter($columns): array {
    // print_rr($columns, 'subs_api_data_table_filter');
    $cols = $criteria = [];
    foreach ($columns as $column => $value) {
        if (empty($value)) continue;
        // print_rr($column, ' val: ' . $value);
        $col_parts = explode("_", $column, 2);
        $table = $col_parts[0];
        $column_name = $col_parts[1];
        $cols["{$table}.{$column_name}"] = ['=', $value];
    }
    if (count($cols) > 0) $criteria["where"] = $cols;
    return $criteria;
}


}