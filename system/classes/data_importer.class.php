<?php
namespace system;

use system\database;
use system\Schema;
use system\table_config_manager;
use system\data_table_generator;

/**
 * DataImporter Class
 *
 * Handles CSV data import with automatic schema detection and typed column creation.
 * All imports create individual typed database columns for optimal performance.
 * Includes automatic table configuration storage and data type detection.
 */
class data_importer {
    public static $log_target = "data_importer";

    /**
     * Process CSV data and import into database
     *
     * @param array $mapping Mapping configuration
     * @param array $header CSV header row
     * @param array $rows CSV data rows
     * @param array $unique_hash Headers to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    private static function process_csv_data($mapping, $header, $rows, $unique_hash = [], $debug = true) {
        $output = "";
        $count = 0;

        foreach ($rows as $row) {
            $csv = array_combine($header, $row);
            $count++;

            $query_results = [];
            $insert_ids = [];
            tcs_log("Processing row: " . implode(',', $row), self::$log_target);
            foreach ($mapping as $groupName => $groupData) {
                $db_table = $groupData['table'];
                $db_key = $groupData['key'];
                $query_array = [];

                // Process regular column mappings
                foreach ($groupData['columns'] as $csv_col => $db_col) {
                    if (isset($csv[$csv_col]) && $csv[$csv_col] !== '') {
                        $query_array[$db_col] = $csv[$csv_col];
                    }
                }
                tcs_log("importing row:
                     table: {$db_table},
                     key: {$db_key},
                     data: " . implode(',', $query_array) . "
                     GroupData: " . print_r($groupData['extra'],true) . "
                     UniqueHash: " . print_r($unique_hash,true) . "
                     InsertIds: " . print_r($insert_ids, true), self::$log_target);

                // Process extra fields
                if (isset($groupData['extra']) && !empty($groupData['extra'])) {
                    $query_array = self::process_extra_fields(
                        $query_array,
                        $groupData['extra'],
                        $csv,
                        $unique_hash,
                        $insert_ids
                    );
                }

                tcs_log("after extra fields data: " . print_r($query_array, true), self::$log_target);

                // Skip if no data to insert
                if (empty($query_array)) {
                    $query_results[$groupName] = 0;
                    continue;
                }

                // Execute database operation
                tcs_log("execute_database_operation:
                     table: {$db_table},
                     key: {$db_key},
                     data: " . implode(',', $query_array), self::$log_target);
                $result = self::execute_database_operation($db_table, $db_key, $query_array);
                if (isset($result['error'])) {
                    return $result;
                }

                $query_results[$groupName] = $result['affected_rows'];
                $insert_ids[$groupName] = $result['insert_id'];
            }

            // Generate debug output
            if ($debug) {
                $output .= self::generate_debug_output($count, $query_results);
            }
        }

        return ['message' => 'CSV data import completed.', 'response' => $output];
    }

    /**
     * Import CSV file into database tables based on mapping configuration
     *
     * @param array $mapping Mapping configuration for database tables and columns
     * @param string $csv_file_path Path to the CSV file
     * @param array $unique_hash Headers to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function import_csv_into_database($mapping, $csv_file_path, $unique_hash = [], $debug = true, $log_target = "data_importer"): array {
        self::$log_target = $log_target;
        return self::import_data_into_database($mapping, $csv_file_path, 'csv', $unique_hash, $debug);
    }


    /**
     * Import data into database tables based on mapping configuration
     * Supports both CSV and JSON data sources
     *
     * @param array $mapping Mapping configuration for database tables and columns
     * @param string $file_path Path to the data file (CSV or JSON)
     * @param string $file_type Type of file ('csv' or 'json')
     * @param array $unique_hash Headers/keys to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function import_data_into_database($mapping, $file_path, $file_type = 'csv', $unique_hash = [], $debug = true, $log_target = "main") {
        self::$log_target = $log_target;
        if (!is_countable($mapping)) return ['error' => tcs_log('Mapping Invalid',self::$log_target)];
        $countmapping = count($mapping);
        tcs_log(print_rr([
            'file_path' => $file_path,
            'file_type' => $file_type,
            'unique_hash' => $unique_hash,
            'debug' => $debug,
            'mapping' => $mapping
        ], "Starting import of {$file_path} into database"), self::$log_target);
        try {
            // Read data file based on type
            if ($file_type === 'csv') {
               tcs_log("Reading csv file: $file_path",self::$log_target);
                $data = self::read_csv_file($file_path);
            } elseif ($file_type === 'json') {
               tcs_log("Reading json file: $file_path", self::$log_target);
                $data = self::read_json_file($file_path);
            } else {
                return ['error' => "Unsupported file type: {$file_type}"];
            }

           tcs_log("mapping has {$countmapping} groups:" . implode(', ', array_keys($mapping)),self::$log_target);
            if (isset($data['error'])) {
                return $data;
            }

            // Process the data
            if ($file_type === 'csv') {
               tcs_log("Processing csv data " . count($data['rows']),self::$log_target);
                return self::process_csv_data($mapping, $data['header'], $data['rows'], $unique_hash, $debug);
            } else {
               tcs_log("Processing JSON data", self::$log_target);
                return self::process_json_data($mapping, $data, $unique_hash, $debug);
            }
        } catch (Exception $e) {
           tcs_log("Error in data import: " . $e->getMessage(), self::$log_target);
            return ['error' => "Import failed: " . $e->getMessage()];
        }
    }

    /**
     * Read JSON file and parse its contents
     *
     * @param string $json_file_path Path to the JSON file
     * @return array Parsed JSON data or error
     */
    private static function read_json_file($json_file_path) {
        if (!file_exists($json_file_path)) {
           tcs_log("File not found: " . $json_file_path, self::$log_target);
            return ['error' => "Could not open JSON file."];
        }

        try {
           tcs_log("Starting import of {$json_file_path}", self::$log_target);

            $json_content = file_get_contents($json_file_path);
            if (empty($json_content)) {
                return ['error' => "JSON file is empty."];
            }

            $data = json_decode($json_content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['error' => "Invalid JSON: " . json_last_error_msg()];
            }

            return $data;
        } catch (Exception $e) {
           tcs_log("Error reading JSON: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to read JSON: " . $e->getMessage()];
        }
    }

    /**
     * Read CSV file and parse its contents
     *
     * @param string $csv_file_path Path to the CSV file
     * @return array Parsed CSV data or error
     */
    public static function read_csv_file($csv_file_path) {
        if (!file_exists($csv_file_path)) {
           tcs_log("File not found: " . $csv_file_path, self::$log_target);
            return ['error' => "Could not open CSV file."];
        }

        try {
           tcs_log("Starting import of {$csv_file_path}", self::$log_target);

            $csv_content = file_get_contents($csv_file_path);
            if (empty($csv_content)) {
                return ['error' => "CSV file is empty."];
            }

            $rows = array_map('str_getcsv', file($csv_file_path));
            $header = array_shift($rows);
            return ['header' => $header, 'rows' => $rows];
        } catch (Exception $e) {
           tcs_log("Error reading CSV: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to read CSV: " . $e->getMessage()];
        }
    }

    /**
     * Process JSON data and import into database
     *
     * @param array $mapping Mapping configuration
     * @param array $json_data JSON data to process
     * @param array $unique_hash Keys to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function process_json_data($mapping, $json_data, $unique_hash = [], $debug = true, $log_target_old = "main"): array {
        $output = "";
        $count = 0;

        foreach ($json_data as $row) {
            $count++;
            $query_results = [];
            $insert_ids = [];

            foreach ($mapping as $groupName => $groupData) {
                $db_table = $groupData['table'];
                $db_key = $groupData['key'];

                // Get the first path to determine if we're dealing with an array
                $first_path = array_key_first($groupData['columns']);
                $array_path = explode('.', $first_path)[0];
                $source_data = self::get_value_from_path($row, $array_path);

                // Skip if source data doesn't exist or is null
                if ($source_data === null || $source_data === '') {
                   tcs_log("Skipping $groupName - no data found at path: $array_path", self::$log_target);
                    continue;
                }

                // Automatically detect if we're dealing with an array
                if (is_array($source_data) && self::array_is_list($source_data)) {
                    // Skip if array is empty
                    if (empty($source_data)) {
                       tcs_log("Skipping $groupName - array is empty at path: $array_path", self::$log_target);
                        continue;
                    }

                    // Handle array data
                    foreach ($source_data as $array_item) {
                        $query_array = [];
                        $has_data = false;

                        foreach ($groupData['columns'] as $json_path => $db_col) {
                            // Remove base path to get relative path within array item
                            $relative_path = substr($json_path, strlen($array_path) + 1);
                            $value = self::get_value_from_path($array_item, $relative_path);
                            if ($value !== null && $value !== '') {
                                $query_array[$db_col] = $value;
                                $has_data = true;
                            }
                        }

                        // Skip if no valid data was found
                        if (!$has_data) {
                           tcs_log(" Skipping array item in $groupName - no valid data found", self::$log_target);
                            continue;
                        }

                        // Process extra fields
                        if (isset($groupData['extra'])) {
                            $query_array = self::process_extra_fields($query_array, $groupData['extra'], $row, $unique_hash, $insert_ids);
                        }

                        // Perform database insert
                        if (!empty($query_array)) {
                            $result = self::execute_database_operation($db_table, $db_key, $query_array);
                            if (isset($result['insert_id'])) {
                                $insert_ids[$groupName] = $result['insert_id'];
                            }
                            $query_results[] = $result;
                        }
                    }
                } else {
                    // Handle non-array data
                    $query_array = [];
                    $has_data = false;

                    foreach ($groupData['columns'] as $json_path => $db_col) {
                        $value = self::get_value_from_path($row, $json_path);
                        if ($value !== null && $value !== '') {
                            $query_array[$db_col] = $value;
                            $has_data = true;
                        }
                    }

                    // Skip if no valid data was found
                    if (!$has_data) {
                       tcs_log("Skipping $groupName - no valid data found", self::$log_target);
                        continue;
                    }

                    if (isset($groupData['extra'])) {
                        $query_array = self::process_extra_fields($query_array, $groupData['extra'], $row, $unique_hash, $insert_ids);
                    }

                    if (!empty($query_array)) {
                        $result = self::execute_database_operation($db_table, $db_key, $query_array);
                        if (isset($result['insert_id'])) {
                            $insert_ids[$groupName] = $result['insert_id'];
                        }
                        $query_results[] = $result;
                    }
                }
            }

            if (!empty($query_results)) {
                $output .= self::format_import_result($count, $query_results);
            }
        }

        return ['success' => true, 'message' => $output];
    }

    /**
     * Format the import result for a processed row
     *
     * @param int $row_number Row number being processed
     * @param array $query_results Results of database operations
     * @return string Formatted result message
     */
    private static function format_import_result($row_number, $query_results) {
        $result = "Line {$row_number}: ";
        foreach ($query_results as $operation_result) {
            if (isset($operation_result['error'])) {
                $result .= "Error: " . $operation_result['error'] . ", ";
            } else {
                $affected = $operation_result['affected_rows'];
                $result .= match ($affected) {
                    1 => "New record inserted, ",
                    2 => "Record updated, ",
                    0 => "No changes needed, ",
                    default => "Unknown result ({$affected}), "
                };
            }
        }
        return $result . "<br>";
    }

    /**
     * Helper function to determine if an array is a list (sequential integer keys)
     * For PHP versions < 8.1 where array_is_list doesn't exist
     */
    private static function array_is_list($arr) {
        if (!is_array($arr)) {
            return false;
        }
        if (empty($arr)) {
            return true;
        }
        if (function_exists('array_is_list')) {
            return array_is_list($arr);
        }
        return array_keys($arr) === range(0, count($arr) - 1);
    }

    /**
     * Execute database insert/update operation
     *
     * @param string $table Database table name
     * @param string $key Primary key column name
     * @param array $data Column data to insert/update
     * @return array Operation result
     */
    private static function execute_database_operation(string $table, string $key, array $data): array {
        $set_strings = $up_strings = $params = [];
        foreach ($data as $column => $value) {
            if (is_array($value)){
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            }
            if (is_array($column)) {
                return [
                    'error' => 'Invalid data type',
                    'details' => "Column is an array",
                    'data' => print_r($value, true)
                ];
            }

            $set_strings[] = "{$column} = :{$column}";
            $params[":{$column}"] = $value;
            if ($column != $key) { // remove the unique/key fields from the update statement
                $up_strings[] = "{$column} = :up_{$column}";
                $params[":up_{$column}"] = $value;
            }
        }
        $set_string = implode(', ', $set_strings);
        $up_string = implode(', ', $up_strings);
        $query_sql = "INSERT INTO {$table} SET {$set_string} ON DUPLICATE KEY UPDATE `id` = LAST_INSERT_ID(`id`), {$up_string};";

        tcs_log("SQL Query: " . $query_sql, self::$log_target);
        tcs_log("SQL Params: " . print_r($params, true), self::$log_target);

        $affected_rows = tep_db_affected_rows(tep_db_query($query_sql, null, $params));
        $insert_id = tep_db_insert_id();

        return [
            'affected_rows' => $affected_rows,
            'insert_id' => $insert_id
        ];
    }

    /**
     * Generate debug output for a processed row
     *
     * @param int $row_number Row number
     * @param array $query_results Results of database operations
     * @return string Debug output
     */
    private static function generate_debug_output($row_number, $query_results) {
        $result = '';
        foreach ($query_results as $groupName => $affected) {
            $result .= "{$groupName}: 1 ";
            $result .= match ($affected) {
                1 => 'inserted, ',
                2 => 'updated, ',
                default => 'skipped, '
            };
        }
        return "Line {$row_number}: {$result}<br>";
    }

    /**
     * Process extra fields in the mapping
     *
     * @param array $query_array Current query data
     * @param array $extra_fields Extra fields configuration
     * @param array $data Current data row (CSV row or flattened JSON)
     * @param array $unique_hash Keys/headers for hash generation
     * @param array $insert_ids Previously generated insert IDs
     * @return array Updated query array
     */
    private static function process_extra_fields($query_array, $extra_fields, $data, $unique_hash, $insert_ids) {
        tcs_log("process_extra_fields: " . print_r($extra_fields, true), self::$log_target);
        foreach ($extra_fields as $db_col => $extra_config) {
            if (preg_match('/<([^>]*)>([^<]*)/i', $extra_config, $matches)) {
                $tag = $matches[1];
                $param = $matches[2] ?? '';

                switch ($tag) {
                    case 'unique_hash':
                    case 'hash_string':
                        // Only calculate hash values once
                        if (!isset($hash_result)) {
                            // Get values for hash from the current data row
                            $hash_values = [];
                            foreach ($unique_hash as $key) {
                                // For CSV data, keys are direct
                                // For JSON data, keys might be paths
                                $value = isset($data[$key]) ? $data[$key] : self::get_value_from_path($data, $key);
                                $hash_values[] = $value ?? '';
                            }
                            tcs_log("           hash_values: " . implode(',', $hash_values), self::$log_target);
                            $hash_result = self::create_unique_hash($hash_values);
                            tcs_log("           hash_result: " . print_r($hash_result, true), self::$log_target);
                        }

                        // Assign the appropriate value based on the tag
                        if ($tag === 'unique_hash') {
                            $query_array[$db_col] = $hash_result['hash'];
                        } else { // hash_string
                            $query_array[$db_col] = $hash_result['string'];
                        }
                        tcs_log("           query_array after {$tag}: " . print_r($query_array, true), self::$log_target);
                        break;

                    case 'group_insert_id':
                        if (isset($insert_ids[$param])) {
                            $query_array[$db_col] = $insert_ids[$param];
                        }
                        break;

                    case 'json_encode':
                        // For JSON data, get the value from the path
                        $value = self::get_value_from_path($data, $param);
                        if ($value !== null) {
                            $query_array[$db_col] = json_encode($value);
                        }
                        break;

                    default:
                        // For direct value assignment
                        $query_array[$db_col] = $data[$extra_config];
                        break;
                }
            } else {
                // Direct value assignment for non-tag values
                $query_array[$db_col] = $data[$extra_config];
            }
        }

        return $query_array;
    }

    /**
     * Parse a JSON string directly into the database
     *
     * @param array $mapping Mapping configuration
     * @param string $json_string JSON string to parse
     * @param array $unique_hash Keys to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function import_json_string($mapping, $json_string, $unique_hash = [], $debug = true) {
        try {
            $data = json_decode($json_string, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['error' => "Invalid JSON: " . json_last_error_msg()];
            }

            return self::process_json_data($mapping, $data, $unique_hash, $debug);
        } catch (Exception $e) {
           tcs_log("Error in JSON import: " . $e->getMessage(), self::$log_target);
            return ['error' => "Import failed: " . $e->getMessage()];
        }
    }

    /**
     * Flatten nested JSON data into an array of rows
     *
     * @param array $json_data Parsed JSON data
     * @return array Flattened data rows
     */
    public static function flatten_json_data($json_data) {
        $flattened = [];
        foreach ($json_data as $key => $item) {
            // Extract line items as they need to be processed as separate rows
            $lineItems = $item['lineItems'] ?? [];

            // Remove line items from the item to create the base record
            unset($item['lineItems']);

            if (empty($lineItems)) {
                // If no line items, just add the item as a single row
                $flattened[] = self::flatten_nested_array($item);
            } else {
                // For each line item, create a row combining item data and line item data
                foreach ($lineItems as $lineItem) {
                    $row = $item;
                    $row['lineItem'] = $lineItem;

                    // Extract bill plans as they might need to be processed separately
                    $billPlans = $lineItem['billPlans'] ?? [];
                    unset($row['lineItem']['billPlans']);

                    if (empty($billPlans)) {
                        $flattened[] = self::flatten_nested_array($row);
                    } else {
                        // For each bill plan, create a row combining item, line item, and bill plan data
                        foreach ($billPlans as $billPlan) {
                            $billPlanRow = $row;
                            $billPlanRow['billPlan'] = $billPlan;
                            $flattened[] = self::flatten_nested_array($billPlanRow);
                        }
                    }
                }
            }
        }
        return $flattened;
    }

    /**
     * Flatten a nested array into a single-level array with dot notation keys
     *
     * @param array $data Nested array
     * @param string $prefix Current key prefix
     * @return array Flattened array
     */
    private static function flatten_nested_array($data, $prefix = '') {
        $result = [];
        foreach ($data as $key => $value) {
            $new_key = $prefix ? "{$prefix}.{$key}" : $key;
            if (is_array($value) && !empty($value) && !isset($value[0])) {
                // It's an associative array, so flatten it
                $result = array_merge($result, self::flatten_nested_array($value, $new_key));
            } else {
                // It's a scalar value or indexed array, store as is
                $result[$new_key] = $value;
            }
        }

        return $result;
    }

    /**
     * Get a value from a flattened array using dot notation path
     *
     * @param array $data Flattened data array
     * @param string $path Dot notation path
     * @return mixed|null Value at the path or null if not found
     */
    private static function get_value_from_path($data, $path) {
        // Direct access if the path exists
        if (isset($data[$path])) {
            return $data[$path];
        }

        // Try to navigate through nested paths
        $keys = explode('.', $path);
        $current = $data;

        foreach ($keys as $key) {
            if (!isset($current[$key])) {
                return null;
            }
            $current = $current[$key];
        }

        return $current;
    }

    /**
     * Create a unique hash from an array of values
     *
     * @param array $columns Values to hash
     * @return array Hash result with original string and hash
     */
    public static function create_unique_hash($columns) {
        $concatString = implode('', $columns);
        /* Generate the hash and add it to the mapped data*/
        $uniqueHash = hash('crc32', $concatString);
        /* Return both the original string and the hash*/
        return [
            'string' => $concatString,
            'hash' => $uniqueHash
        ];
    }

    /**
     * Function to log messages
     *
     * @param mixed ...$args Arguments to log
     * @return string The logged message
     */

    /**
     * Update unique hashes in a database table for existing records
     *
     * @param string $table Database table name
     * @param string $hash_column Column name where the hash is stored
     * @param string $string_column Column name where the original string is stored (optional)
     * @param array $source_columns Array of column names to use for generating the hash
     * @param int $batch_size Number of records to process in each batch (default: 100)
     * @return array Result information with counts of updated records
     */
    public static function update_database_hashes($table, $hash_column, $source_columns, $string_column = null, $batch_size = 100) {
        $total_records = 0;
        $updated_records = 0;
        $failed_records = 0;
        $offset = 0;

        try {
            // Get total count for progress reporting
            $count_query = "SELECT COUNT(*) as total FROM {$table}";
            $count_result = tep_db_query($count_query);
            $count_row = tep_db_fetch_array($count_result);
            $total_count = $count_row['total'];

            tcs_log("Starting hash update for {$table}. Total records: {$total_count}", self::$log_target);

            // Process records in batches
            while (true) {
                // Select a batch of records
                $select_columns = $source_columns;
                if (!in_array('id', $select_columns)) {
                    $select_columns[] = 'id';
                }

                $columns_str = implode(', ', $select_columns);
                $query = "SELECT {$columns_str} FROM {$table} LIMIT {$batch_size} OFFSET {$offset}";
                $result = tep_db_query($query);

                $batch_count = 0;
                $batch_updates = [];

                // Process each record in the batch
                while ($row = tep_db_fetch_array($result)) {
                    $batch_count++;
                    $total_records++;

                    // Extract values for hash generation
                    $hash_values = [];
                    foreach ($source_columns as $column) {
                        if (isset($row[$column])) {
                            $hash_values[] = $row[$column];
                        } else {
                            $hash_values[] = '';
                        }
                    }

                    // Generate new hash
                    $hash_result = self::create_unique_hash($hash_values);

                    // Prepare update data
                    $update_data = [
                        'id' => $row['id'],
                        'new_hash' => $hash_result['hash']
                    ];

                    if ($string_column) {
                        $update_data['new_string'] = $hash_result['string'];
                    }

                    $batch_updates[] = $update_data;
                }

                // If no records were found, we're done
                if ($batch_count === 0) {
                    break;
                }

                // Update records in batch
                foreach ($batch_updates as $update) {
                    $update_query = "UPDATE {$table} SET {$hash_column} = :new_hash";
                    $params = [':new_hash' => $update['new_hash']];

                    if ($string_column && isset($update['new_string'])) {
                        $update_query .= ", {$string_column} = :new_string";
                        $params[':new_string'] = $update['new_string'];
                    }

                    $update_query .= " WHERE id = :id";
                    $params[':id'] = $update['id'];

                    try {
                        $update_result = tep_db_query($update_query, null, $params);
                        $affected = tep_db_affected_rows($update_result);
                        if ($affected > 0) {
                            $updated_records++;
                        }
                    } catch (Exception $e) {
                        tcs_log("Error updating hash for record ID {$update['id']}: " . $e->getMessage(), self::$log_target);
                        $failed_records++;
                    }
                }

                // Move to next batch
                $offset += $batch_size;

                // Log progress
                $progress = round(($total_records / $total_count) * 100, 2);
                tcs_log("Processed {$total_records}/{$total_count} records ({$progress}%)", self::$log_target);
            }

            return [
                'success' => true,
                'message' => "Hash update completed for {$table}",
                'total_records' => $total_records,
                'updated_records' => $updated_records,
                'failed_records' => $failed_records
            ];

        } catch (Exception $e) {
            tcs_log("Error in hash update process: " . $e->getMessage(), self::$log_target);
            return [
                'error' => "Hash update failed: " . $e->getMessage(),
                'total_records' => $total_records,
                'updated_records' => $updated_records,
                'failed_records' => $failed_records
            ];
        }
    }

    /**
     * Update unique hashes in the products_autodesk_catalog table and related tables
     *
     * This function updates the unique hashes in the products_autodesk_catalog table
     * and also updates any related tables that reference these hashes to maintain
     * database integrity. It uses the existing hash_string column to generate new hashes.
     *
     * @param int $batch_size Number of records to process in each batch (default: 100)
     * @return array Result information with counts of updated records
     */
    public static function update_autodesk_catalog_hashes($batch_size = 100) {
        $total_records = 0;
        $updated_catalog_records = 0;
        $updated_relation_records = 0;
        $failed_records = 0;
        $offset = 0;

        // Define the main table and columns
        $main_table = 'products_autodesk_catalog';
        $hash_column = 'unique_hash';
        $string_column = 'hash_string';

        // Define related tables that use the unique_hash field
        $related_tables = [
            'products_to_autodesk_catalog' => [
                'hash_column' => 'unique_hash',
                'id_column' => 'products_to_autodesk_catalog_id'
            ],
            'products_variations' => [
                'hash_column' => 'autodesk_catalog_unique_hash',
                'id_column' => 'products_variations_id'
            ]
        ];

        try {
            // Get total count for progress reporting
            $count_query = "SELECT COUNT(*) as total FROM {$main_table}";
            $count_result = tep_db_query($count_query);
            $count_row = tep_db_fetch_array($count_result);
            $total_count = $count_row['total'];

            tcs_log("Starting hash update for {$main_table}. Total records: {$total_count}", self::$log_target);

            // Create a mapping of old hashes to new hashes
            $hash_mapping = [];

            // Process records in batches
            while (true) {
                // Select a batch of records with id, unique_hash, and hash_string
                $query = "SELECT id, {$hash_column}, {$string_column} FROM {$main_table} LIMIT {$batch_size} OFFSET {$offset}";
                $result = tep_db_query($query);

                $batch_count = 0;
                $batch_updates = [];

                // Process each record in the batch
                while ($row = tep_db_fetch_array($result)) {
                    $batch_count++;
                    $total_records++;

                    // Get the existing hash string
                    $hash_string = $row[$string_column];

                    // Generate new hash from the existing hash string
                    $new_hash = hash('crc32', $hash_string);
                    $old_hash = $row[$hash_column];

                    // Only update if the hash has changed
                    if ($new_hash !== $old_hash) {
                        // Store the mapping of old hash to new hash
                        $hash_mapping[$old_hash] = $new_hash;

                        // Prepare update data
                        $update_data = [
                            'id' => $row['id'],
                            'old_hash' => $old_hash,
                            'new_hash' => $new_hash
                        ];

                        $batch_updates[] = $update_data;
                    }
                }

                // If no records were found, we're done
                if ($batch_count === 0) {
                    break;
                }

                // Update records in batch
                foreach ($batch_updates as $update) {
                    $update_query = "UPDATE {$main_table} SET {$hash_column} = :new_hash WHERE id = :id";
                    $params = [
                        ':new_hash' => $update['new_hash'],
                        ':id' => $update['id']
                    ];

                    try {
                        $update_result = tep_db_query($update_query, null, $params);
                        $affected = tep_db_affected_rows($update_result);
                        if ($affected > 0) {
                            $updated_catalog_records++;
                        }
                    } catch (Exception $e) {
                        tcs_log("Error updating hash for record ID {$update['id']}: " . $e->getMessage(), self::$log_target);
                        $failed_records++;
                    }
                }

                // Move to next batch
                $offset += $batch_size;

                // Log progress
                $progress = round(($total_records / $total_count) * 100, 2);
                tcs_log("Processed {$total_records}/{$total_count} records ({$progress}%)", self::$log_target);
            }

            // Only update related tables if we have hash mappings
            if (!empty($hash_mapping)) {
                // Now update related tables using the hash mapping
                foreach ($related_tables as $table_name => $table_info) {
                    $related_hash_column = $table_info['hash_column'];
                    $related_id_column = $table_info['id_column'];

                    tcs_log("Updating related table: {$table_name}", self::$log_target);

                    // Get all records from the related table
                    $related_query = "SELECT {$related_id_column}, {$related_hash_column} FROM {$table_name}";
                    $related_result = tep_db_query($related_query);

                    $related_updates = 0;
                    $related_failures = 0;

                    // Process each record
                    while ($related_row = tep_db_fetch_array($related_result)) {
                        $old_related_hash = $related_row[$related_hash_column];

                        // Check if we have a mapping for this hash
                        if (isset($hash_mapping[$old_related_hash])) {
                            $new_related_hash = $hash_mapping[$old_related_hash];

                            // Update the record
                            $related_update_query = "UPDATE {$table_name} SET {$related_hash_column} = :new_hash WHERE {$related_id_column} = :id";
                            $related_params = [
                                ':new_hash' => $new_related_hash,
                                ':id' => $related_row[$related_id_column]
                            ];

                            try {
                                $related_update_result = tep_db_query($related_update_query, null, $related_params);
                                $related_affected = tep_db_affected_rows($related_update_result);
                                if ($related_affected > 0) {
                                    $related_updates++;
                                    $updated_relation_records++;
                                }
                            } catch (Exception $e) {
                                tcs_log("Error updating related hash in {$table_name} for ID {$related_row[$related_id_column]}: " . $e->getMessage(), self::$log_target);
                                $related_failures++;
                                $failed_records++;
                            }
                        }
                    }

                    tcs_log("Updated {$related_updates} records in {$table_name}, {$related_failures} failures", self::$log_target);
                }
            } else {
                tcs_log("No hash mappings found, skipping related table updates", self::$log_target);
            }

            return [
                'success' => true,
                'message' => "Hash update completed for {$main_table} and related tables",
                'total_records' => $total_records,
                'updated_catalog_records' => $updated_catalog_records,
                'updated_relation_records' => $updated_relation_records,
                'failed_records' => $failed_records
            ];

        } catch (Exception $e) {
            tcs_log("Error in hash update process: " . $e->getMessage(), self::$log_target);
            return [
                'error' => "Hash update failed: " . $e->getMessage(),
                'total_records' => $total_records,
                'updated_catalog_records' => $updated_catalog_records,
                'updated_relation_records' => $updated_relation_records,
                'failed_records' => $failed_records
            ];
        }
    }

    /**
     * Analyze CSV structure and detect data types
     *
     * @param string $csv_file_path Path to CSV file
     * @param int $sample_rows Number of rows to sample for analysis (default: 100)
     * @return array Analysis result with headers, data types, and sample data
     */
    public static function analyze_csv_structure(string $csv_file_path, int $sample_rows = 100): array {
        try {
            $csv_result = self::read_csv_file($csv_file_path);
            if (isset($csv_result['error'])) {
                return $csv_result;
            }

            $headers = $csv_result['header'];
            $rows = array_slice($csv_result['rows'], 0, $sample_rows);

            $data_types = [];
            $sample_data = [];

            // Initialize analysis for each column
            foreach ($headers as $index => $header) {
                $data_types[$header] = self::detect_column_data_type($rows, $index);
                $sample_data[$header] = array_slice(array_column($rows, $index), 0, 5);
            }

            return [
                'success' => true,
                'headers' => $headers,
                'data_types' => $data_types,
                'sample_data' => $sample_data,
                'total_rows' => count($csv_result['rows']),
                'analyzed_rows' => count($rows)
            ];

        } catch (Exception $e) {
            tcs_log("Error analyzing CSV structure: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to analyze CSV: " . $e->getMessage()];
        }
    }

    /**
     * Detect data type for a specific column based on sample data
     *
     * @param array $rows Sample rows
     * @param int $column_index Column index to analyze
     * @return string Detected data type
     */
    private static function detect_column_data_type(array $rows, int $column_index): string {
        $values = array_column($rows, $column_index);
        $non_empty_values = array_filter($values, function($val) {
            return $val !== '' && $val !== null;
        });

        if (empty($non_empty_values)) {
            return 'string';
        }

        $type_scores = [
            'integer' => 0,
            'decimal' => 0,
            'date' => 0,
            'datetime' => 0,
            'boolean' => 0,
            'string' => 0
        ];

        foreach ($non_empty_values as $value) {
            // Check for integer
            if (is_numeric($value) && (int)$value == $value) {
                $type_scores['integer']++;
            }
            // Check for decimal
            elseif (is_numeric($value)) {
                $type_scores['decimal']++;
            }
            // Check for date formats
            elseif (self::is_date_format($value)) {
                if (self::has_time_component($value)) {
                    $type_scores['datetime']++;
                } else {
                    $type_scores['date']++;
                }
            }
            // Check for boolean
            elseif (self::is_boolean_format($value)) {
                $type_scores['boolean']++;
            }
            // Default to string
            else {
                $type_scores['string']++;
            }
        }

        // Return the type with highest score, with minimum threshold
        $total_values = count($non_empty_values);
        $threshold = $total_values * 0.8; // 80% of values must match type

        foreach (['integer', 'decimal', 'datetime', 'date', 'boolean'] as $type) {
            if ($type_scores[$type] >= $threshold) {
                return $type;
            }
        }

        return 'string'; // Default fallback
    }

    /**
     * Check if a value matches common date formats
     */
    private static function is_date_format(string $value): bool {
        $date_patterns = [
            '/^\d{4}-\d{2}-\d{2}$/',                    // YYYY-MM-DD
            '/^\d{2}\/\d{2}\/\d{4}$/',                  // MM/DD/YYYY
            '/^\d{2}-\d{2}-\d{4}$/',                    // MM-DD-YYYY
            '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/',  // YYYY-MM-DD HH:MM:SS
            '/^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}$/' // MM/DD/YYYY HH:MM:SS
        ];

        foreach ($date_patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return strtotime($value) !== false;
            }
        }

        return false;
    }

    /**
     * Check if a date value has time component
     */
    private static function has_time_component(string $value): bool {
        return strpos($value, ':') !== false;
    }

    /**
     * Check if a value is boolean format
     */
    private static function is_boolean_format(string $value): bool {
        $boolean_values = ['true', 'false', 'yes', 'no', '1', '0', 'y', 'n'];
        return in_array(strtolower(trim($value)), $boolean_values);
    }

    /**
     * Generate enhanced schema from CSV analysis
     *
     * @param array $analysis_result Result from analyze_csv_structure
     * @param string $table_name Target table name
     * @param string $primary_key Primary key column name (default: 'id')
     * @return array Enhanced schema definition
     */
    public static function generate_enhanced_schema(array $analysis_result, string $table_name, string $primary_key = 'id'): array {
        if (isset($analysis_result['error'])) {
            return $analysis_result;
        }

        $schema = [
            'table_name' => $table_name,
            'primary_key' => $primary_key,
            'columns' => [],
            'mapping' => [
                'main' => [
                    'table' => $table_name,
                    'key' => $primary_key,
                    'columns' => [],
                    'data_types' => []
                ]
            ]
        ];

        // Add auto-increment primary key
        $schema['columns'][$primary_key] = [
            'type' => 'increments',
            'nullable' => false
        ];

        // Process each analyzed column
        foreach ($analysis_result['headers'] as $header) {
            $clean_column_name = self::sanitize_column_name($header);

            // Use intelligent naming if available
            $intelligent_column_name = self::get_intelligent_column_name($table_name, $header, $analysis_result['headers'], $analysis_result['sample_data'][$header] ?? []);
            if ($intelligent_column_name !== $clean_column_name) {
                tcs_log("Intelligent column naming: '{$header}' -> '{$intelligent_column_name}' (was: {$clean_column_name})", self::$log_target);
                $clean_column_name = $intelligent_column_name;
            }

            $data_type = $analysis_result['data_types'][$header];

            // Map to database column type
            $column_definition = self::map_to_database_type($data_type);
            $schema['columns'][$clean_column_name] = $column_definition;

            // Add to mapping configuration
            $schema['mapping']['main']['columns'][$header] = $clean_column_name;
            $schema['mapping']['main']['data_types'][$clean_column_name] = $data_type;
        }

        // Add timestamps
        $schema['columns']['created_at'] = ['type' => 'timestamp', 'default' => 'CURRENT_TIMESTAMP'];
        $schema['columns']['updated_at'] = ['type' => 'timestamp', 'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'];

        return ['success' => true, 'schema' => $schema];
    }

    /**
     * Get intelligent column name using column analyzer
     *
     * @param string $table_name Target table name
     * @param string $original_header Original CSV header
     * @param array $all_headers All CSV headers for context
     * @param array $sample_data Sample data for this column
     * @return string Intelligent column name
     */
    private static function get_intelligent_column_name(string $table_name, string $original_header, array $all_headers, array $sample_data = []): string {
        try {
            // First sanitize the original header
            $sanitized_name = self::sanitize_column_name($original_header);

            // Use column analyzer if available
            if (class_exists('system\column_analyzer')) {
                // Prepare all column names for context analysis
                $all_column_names = array_map([self::class, 'sanitize_column_name'], $all_headers);

                // Analyze the column
                $analysis = \system\column_analyzer::analyze_column($table_name, $sanitized_name, $all_column_names);

                // If we have a confident suggestion and it's different, use it
                if ($analysis['confidence'] >= 30 && $analysis['suggested_name'] !== $sanitized_name) {
                    $suggested_name = $analysis['suggested_name'];

                    // Make sure the suggested name is database-safe
                    $intelligent_name = self::sanitize_column_name($suggested_name);

                    tcs_log("Column analyzer suggestion: '{$original_header}' -> '{$intelligent_name}' (confidence: {$analysis['confidence']}%)", self::$log_target);

                    return $intelligent_name;
                }
            }
        } catch (\Exception $e) {
            tcs_log("Error in intelligent column naming for '{$original_header}': " . $e->getMessage(), self::$log_target);
        }

        // Fall back to sanitized name
        return $sanitized_name;
    }

    /**
     * Sanitize column name for database use
     */
    private static function sanitize_column_name(string $name): string {
        // Convert to lowercase and replace spaces/special chars with underscores
        $clean = strtolower(trim($name));
        $clean = preg_replace('/[^a-z0-9_]/', '_', $clean);
        $clean = preg_replace('/_+/', '_', $clean); // Remove multiple underscores
        $clean = trim($clean, '_'); // Remove leading/trailing underscores

        // Ensure it doesn't start with a number
        if (preg_match('/^[0-9]/', $clean)) {
            $clean = 'col_' . $clean;
        }

        // Ensure it's not empty
        if (empty($clean)) {
            $clean = 'unnamed_column';
        }

        return $clean;
    }

    /**
     * Map detected data type to database column type
     */
    private static function map_to_database_type(string $data_type): array {
        switch ($data_type) {
            case 'integer':
                return ['type' => 'integer', 'nullable' => true];
            case 'decimal':
                return ['type' => 'decimal', 'precision' => 10, 'scale' => 2, 'nullable' => true];
            case 'date':
                return ['type' => 'date', 'nullable' => true];
            case 'datetime':
                return ['type' => 'timestamp', 'nullable' => true];
            case 'boolean':
                return ['type' => 'boolean', 'nullable' => true];
            case 'text':
                return ['type' => 'text', 'nullable' => true];
            case 'string':
            default:
                return ['type' => 'string', 'length' => 255, 'nullable' => true];
        }
    }

    /**
     * Create database table from enhanced schema
     *
     * @param array $schema Enhanced schema definition
     * @return array Creation result
     */
    public static function create_table_from_schema(array $schema): array {
        try {
            $table_name = $schema['table_name'];

            // Check if table already exists
            if (Schema::hasTable($table_name)) {
                return ['error' => "Table {$table_name} already exists"];
            }

            // Create table using Blueprint
            Schema::create($table_name, function($table) use ($schema) {
                foreach ($schema['columns'] as $column_name => $definition) {
                    self::add_column_to_blueprint($table, $column_name, $definition);
                }
            });

            tcs_log("Successfully created table: {$table_name}", self::$log_target);

            return [
                'success' => true,
                'message' => "Table {$table_name} created successfully",
                'table_name' => $table_name
            ];

        } catch (Exception $e) {
            tcs_log("Error creating table from schema: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to create table: " . $e->getMessage()];
        }
    }

    /**
     * Add column to Blueprint based on definition
     */
    private static function add_column_to_blueprint($table, string $column_name, array $definition) {
        $type = $definition['type'];

        switch ($type) {
            case 'increments':
                $column = $table->increments($column_name);
                break;
            case 'integer':
                $column = $table->integer($column_name);
                break;
            case 'decimal':
                $precision = $definition['precision'] ?? 8;
                $scale = $definition['scale'] ?? 2;
                $column = $table->decimal($column_name, $precision, $scale);
                break;
            case 'string':
                $length = $definition['length'] ?? 255;
                $column = $table->string($column_name, $length);
                break;
            case 'text':
                $column = $table->text($column_name);
                break;
            case 'date':
                $column = $table->date($column_name);
                break;
            case 'timestamp':
                // Handle timestamp columns with special default values
                if (isset($definition['default'])) {
                    if ($definition['default'] === 'CURRENT_TIMESTAMP') {
                        // Use the Blueprint's custom column method to avoid quoting issues
                        $table->addCustomColumn("`{$column_name}` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP");
                        return; // Exit early since we've added the column directly
                    } elseif ($definition['default'] === 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP') {
                        // Use the Blueprint's custom column method to avoid quoting issues
                        $table->addCustomColumn("`{$column_name}` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                        return; // Exit early since we've added the column directly
                    }
                }
                // Fallback to regular timestamp column
                $column = $table->timestamp($column_name);
                break;
            case 'boolean':
                $column = $table->boolean($column_name);
                break;
            default:
                $column = $table->string($column_name, 255);
        }

        // Apply nullable if specified (only if we haven't already handled the column above)
        if (isset($definition['nullable']) && $definition['nullable']) {
            $column->nullable();
        }
    }

    /**
     * Import CSV data with automatic schema detection and typed columns
     *
     * @param string $table_name Database table name
     * @param string $csv_data_or_path Path to CSV file or CSV data string
     * @param bool $is_file_path Whether the second parameter is a file path or CSV data
     * @param bool $replace_all Whether to clear existing data first
     * @return array Import result
     */
    public static function import_csv_to_hilt_table(string $table_name, string $csv_data_or_path, bool $is_file_path = true, bool $replace_all = false): array {
        try {
            tcs_log("import_csv_to_hilt_table called with table: {$table_name}, is_file_path: " . ($is_file_path ? 'true' : 'false'), self::$log_target);

            // Convert string data to file if needed
            if (!$is_file_path) {
                $temp_file = tempnam(sys_get_temp_dir(), 'hilt_csv_');
                file_put_contents($temp_file, $csv_data_or_path);
                $csv_file_path = $temp_file;
                $cleanup_temp = true;
                tcs_log("Created temp file: {$csv_file_path}", self::$log_target);
            } else {
                $csv_file_path = $csv_data_or_path;
                $cleanup_temp = false;
                tcs_log("Using file path: {$csv_file_path}", self::$log_target);
            }

            // Use enhanced import with auto-schema detection
            tcs_log("Calling import_csv_with_auto_schema for table: {$table_name}", self::$log_target);
            $result = self::import_csv_with_auto_schema($csv_file_path, $table_name, true, $replace_all);
            tcs_log("import_csv_with_auto_schema returned: " . json_encode($result), self::$log_target);

            // Cleanup temporary file if created
            if ($cleanup_temp && isset($temp_file)) {
                unlink($temp_file);
            }

            return $result;

        } catch (\Exception $e) {
            // Cleanup on error
            if (isset($cleanup_temp) && $cleanup_temp && isset($temp_file) && file_exists($temp_file)) {
                unlink($temp_file);
            }

            tcs_log("Error importing CSV: " . $e->getMessage(), self::$log_target);
            return ['error' => "Import failed: " . $e->getMessage()];
        }
    }

    /**
     * Enhanced CSV import with automatic table creation
     *
     * @param string $csv_file_path Path to CSV file
     * @param string $table_name Target table name
     * @param bool $analyze_types Whether to analyze and create typed columns
     * @param bool $replace_table Whether to drop and recreate table if it exists
     * @return array Import result
     */
    public static function import_csv_with_auto_schema(string $csv_file_path, string $table_name, bool $analyze_types = true, bool $replace_table = false): array {
        try {
            // Step 1: Analyze CSV structure
            if ($analyze_types) {
                tcs_log("Analyzing CSV structure for: {$csv_file_path}", self::$log_target);
                $analysis = self::analyze_csv_structure($csv_file_path);
                if (isset($analysis['error'])) {
                    tcs_log("CSV analysis failed: " . $analysis['error'], self::$log_target);
                    return $analysis;
                }
                tcs_log("CSV analysis completed successfully", self::$log_target);

                // Step 2: Generate enhanced schema
                tcs_log("Generating enhanced schema for table: {$table_name}", self::$log_target);
                $schema_result = self::generate_enhanced_schema($analysis, $table_name);
                if (isset($schema_result['error'])) {
                    tcs_log("Schema generation failed: " . $schema_result['error'], self::$log_target);
                    return $schema_result;
                }
                tcs_log("Schema generation completed successfully", self::$log_target);

                $schema = $schema_result['schema'];

                // Step 3: Handle existing table
                if (Schema::hasTable($table_name)) {
                    if ($replace_table) {
                        Schema::drop($table_name);
                        tcs_log("Dropped existing table: {$table_name}", self::$log_target);
                    } else {
                        return ['error' => "Table {$table_name} already exists. Use replace_table=true to overwrite."];
                    }
                }

                // Step 4: Create table with enhanced schema
                $create_result = self::create_table_from_schema($schema);
                if (isset($create_result['error'])) {
                    return $create_result;
                }

                // Step 5: Import data using enhanced mapping with typed columns
                $import_result = self::import_csv_with_typed_columns($csv_file_path, $schema);

                if (isset($import_result['error'])) {
                    return $import_result;
                }

                // Step 6: Generate and store table configuration for performance
                tcs_log("Generating and storing table configuration for: {$table_name}", self::$log_target);
                $config_result = self::generate_and_store_table_config($table_name, $schema, $analysis);
                tcs_log("Table configuration result: " . json_encode($config_result), self::$log_target);

                return [
                    'success' => true,
                    'message' => "CSV imported with auto-generated schema and stored configuration",
                    'table_name' => $table_name,
                    'analysis' => $analysis,
                    'schema' => $schema,
                    'import_result' => $import_result,
                    'config_result' => $config_result
                ];
            } else {
                // Fallback to simple hilt table import
                return self::import_csv_to_hilt_table($table_name, $csv_file_path, true, $replace_table);
            }

        } catch (\Exception $e) {
            tcs_log("Error in enhanced CSV import: " . $e->getMessage(), self::$log_target);
            return ['error' => "Enhanced import failed: " . $e->getMessage()];
        }
    }

    /**
     * Import CSV data into typed columns based on enhanced schema
     *
     * @param string $csv_file_path Path to CSV file
     * @param array $schema Enhanced schema definition
     * @return array Import result
     */
    public static function import_csv_with_typed_columns(string $csv_file_path, array $schema): array {
        try {
            $csv_result = self::read_csv_file($csv_file_path);
            if (isset($csv_result['error'])) {
                return $csv_result;
            }

            $headers = $csv_result['header'];
            $rows = $csv_result['rows'];
            $table_name = $schema['table_name'];
            $mapping = $schema['mapping']['main'];
            $imported_count = 0;
            $failed_count = 0;

            tcs_log("Starting typed column import for table: {$table_name}", self::$log_target);

            foreach ($rows as $row_index => $row) {
                if (count($row) !== count($headers)) {
                    $failed_count++;
                    continue; // Skip malformed rows
                }

                // Create associative array from CSV row
                $csv_data = array_combine($headers, $row);
                $insert_data = [];

                // Map CSV data to database columns with type conversion
                foreach ($mapping['columns'] as $csv_column => $db_column) {
                    if (isset($csv_data[$csv_column])) {
                        $raw_value = $csv_data[$csv_column];
                        $data_type = $mapping['data_types'][$db_column] ?? 'string';

                        // Convert value based on detected data type
                        $converted_value = self::convert_value_to_type($raw_value, $data_type);
                        $insert_data[$db_column] = $converted_value;
                    }
                }

                // Add timestamps
                $insert_data['created_at'] = date('Y-m-d H:i:s');
                $insert_data['updated_at'] = date('Y-m-d H:i:s');

                try {
                    database::table($table_name)->insert($insert_data);
                    $imported_count++;
                } catch (\Exception $e) {
                    tcs_log("Failed to insert row {$row_index}: " . $e->getMessage(), self::$log_target);
                    $failed_count++;
                }
            }

            tcs_log("Typed column import completed: {$imported_count} success, {$failed_count} failed", self::$log_target);

            return [
                'success' => true,
                'message' => "Successfully imported {$imported_count} rows into typed columns",
                'imported_count' => $imported_count,
                'failed_count' => $failed_count,
                'table_name' => $table_name
            ];

        } catch (\Exception $e) {
            tcs_log("Error in typed column import: " . $e->getMessage(), self::$log_target);
            return ['error' => "Typed column import failed: " . $e->getMessage()];
        }
    }

    /**
     * Convert value to appropriate type based on detected data type
     *
     * @param string $value Raw CSV value
     * @param string $data_type Detected data type
     * @return mixed Converted value
     */
    private static function convert_value_to_type(string $value, string $data_type) {
        // Handle empty values
        if ($value === '' || $value === null) {
            return null;
        }

        switch ($data_type) {
            case 'integer':
                return is_numeric($value) ? (int)$value : null;

            case 'decimal':
                return is_numeric($value) ? (float)$value : null;

            case 'boolean':
                $lower_value = strtolower(trim($value));
                $boolean_map = ['true' => 1, 'false' => 0, 'yes' => 1, 'no' => 0, 'y' => 1, 'n' => 0, '1' => 1, '0' => 0];
                return isset($boolean_map[$lower_value]) ? $boolean_map[$lower_value] : null;

            case 'date':
                $timestamp = strtotime($value);
                return $timestamp !== false ? date('Y-m-d', $timestamp) : null;

            case 'datetime':
                $timestamp = strtotime($value);
                return $timestamp !== false ? date('Y-m-d H:i:s', $timestamp) : null;

            case 'string':
            case 'text':
            default:
                return (string)$value;
        }
    }

    /**
     * Generate and store table configuration for performance
     *
     * @param string $table_name Database table name
     * @param array $schema Enhanced schema definition
     * @param array $analysis CSV analysis results
     * @return array Storage result
     */
    private static function generate_and_store_table_config(string $table_name, array $schema, array $analysis): array {
        try {
            // Generate initial table configuration
            $criteria = ['limit' => 30]; // Default criteria for initial config
            $options = [
                'title' => ucfirst(str_replace('_', ' ', $table_name)),
                'description' => "Auto-generated table from CSV import ({$analysis['total_rows']} rows, {$analysis['analyzed_rows']} analyzed)",
                'show_system_columns' => false,
                'hidden_columns' => ['created_at', 'updated_at'], // Hide timestamps by default
                'column_labels' => [],
                'table_name' => $table_name,
                'route_key' => str_replace('autobooks_import_', '', str_replace('_data', '', $table_name)),
                'use_intelligent_naming' => true,
                'use_intelligent_column_selection' => true, // Enable smart column subset selection
                'max_visible_columns' => 8, // Show max 8 columns by default
                'high_relevance_threshold' => 50, // Lower threshold to show more columns initially
                'data_source' => 'csv'
            ];

            $config_result = data_table_generator::generate_table_config($table_name, $criteria, $options);

            if (isset($config_result['error'])) {
                tcs_log("Failed to generate table config: " . $config_result['error'], self::$log_target);
                return ['error' => $config_result['error']];
            }

            $config = $config_result['config'];

            // Store the configuration
            $route_key = 'enhanced_' . $table_name;
            $store_options = [
                'description' => $options['description'],
                'data_source' => 'csv'
            ];

            $store_result = table_config_manager::store_table_config(
                $table_name,
                $route_key,
                $schema,
                $config,
                $store_options
            );

            if (isset($store_result['error'])) {
                tcs_log("Failed to store table config: " . $store_result['error'], self::$log_target);
                return ['error' => $store_result['error']];
            }

            tcs_log("Successfully generated and stored table configuration for: {$table_name}", self::$log_target);

            return [
                'success' => true,
                'message' => 'Table configuration generated and stored successfully',
                'config' => $config,
                'route_key' => $route_key
            ];

        } catch (\Exception $e) {
            tcs_log("Error generating/storing table config: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to generate/store table configuration: " . $e->getMessage()];
        }
    }
}
