<?php
namespace system;
use edge\edge;

class router {
    protected static tcs $tcs;
    public static $routes = array();

    public function __construct($tcs = 'tcs') {
        self::$tcs = $tcs;
    }


    public static function route() {
        $app_path = APP_PATH;
        $current_page = CURRENT_PAGE;        // Check permissions before routing

        print_rr("starting route for {$app_path}/{$current_page}");
        //if (defined(SOURCE_PATH)) $src_app_path = SOURCE_PATH . SOURCE_PAGE;
        $endpoint = CURRENT_PAGE;
        $public = false;
        $target_edge = "layout-main";
        $function_call = false;
        $route_error = false;
        $error_message = '';

        // Special route handlers
        $special_routes = ["scheduled", "api", "settings", "reset-password", "login", "logout"];
        $is_special_route = in_array(TOP_LEVEL, $special_routes);

        // Handle special routes first
        switch (TOP_LEVEL) {
            case "scheduled":
                $sched_file = "resources/scheduled_scripts/{$current_page}.sched.php";
                if (file_exists($sched_file)) {
                    $endpoint = $sched_file;
                } else {
                    $error_message = "Scheduled script not found: {$sched_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                break;
            case "api":
                $parts = explode('/', $app_path);
                $function_call = $current_page;
                $api_result = self::get_api(implode('/', $parts));

                if ($api_result['status'] === 'success') {
                    $endpoint = $api_result['path'];
                } else {
                    $error_message = $api_result['message'];
                    $route_error = true;
                }
                print_rr([
                    'parts' => $parts,
                    'endpoint' => $endpoint,
                    'function_call' => $function_call,
                    'api_result' => $api_result,
                    'api_result_status' => $api_result['status'] ?? 'no status'
                ], "endpointy");
                $target_edge = "layout-api";
                break;
            case "settings":
                $settings_file = tcs_path(str_replace('//', '/', "resources/views/" . SOURCE_APP_PATH . "/" . SOURCE_PAGE . "/" . SOURCE_PAGE . ".settings.php"));
                if (file_exists($settings_file)) {
                    $endpoint = $settings_file;
                } else {
                    $error_message = "Settings file not found: {$settings_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                $target_edge = "layout-view";
                break;
            case "reset-password":
                // Show reset password page
                $reset_file = "system/views/login/reset-password.view.php";
                if (file_exists($reset_file)) {
                    $endpoint = $reset_file;
                } else {
                    $error_message = "Reset password file not found: {$reset_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                $target_edge = "layout-blank";
                $public = true;
                break;
            case "login":
                if (users::checkAuth()) {
                    // If already authenticated, do a clean redirect to dashboard
                    if (!headers_sent()) {
                        header("HTTP/1.1 303 See Other");
                        header("Location: " . rtrim(APP_ROOT, '/'));
                        exit();
                    } else {
                        echo "<script>window.location.href = '" . rtrim(APP_ROOT, '/') . "';</script>";
                        exit();
                    }
                } else {
                    // Show login page
                    $login_file = "system/views/login/login.view.php";
                    if (file_exists($login_file)) {
                        $endpoint = $login_file;
                    } else {
                        $error_message = "Login file not found: {$login_file}";
                        tcs_log($error_message,'router_errors');
                        $route_error = true;
                    }
                    $target_edge = "layout-blank";
                    $public = true;
                }
                break;

            case "logout":
                users::logout();
                $login_file = "system/views/login/login.view.php";
                if (file_exists($login_file)) {
                    $endpoint = $login_file;
                } else {
                    $error_message = "Login file not found after logout: {$login_file}";
                    tcs_log($error_message,'router_errors');
                    $route_error = true;
                }
                $target_edge = "layout-blank";
                break;
        }

        // For all non-special routes (including the default case), treat as a view route
        if (!$is_special_route) {
            // Handle as a view route (previously get_view)
            $route_result = self::get_route($app_path);

            if ($route_result['status'] === 'success') {
                $endpoint = $route_result['path'];
            } else {
                $error_message = $route_result['message'];
                $route_error = true;
            }

            // Check if this is an HTMX request
            $is_htmx_request = isset($_SERVER['HTTP_HX_REQUEST']);

            // Only use layout-view for HTMX requests
            // For direct URL access (non-HTMX), always use layout-main
            if ($is_htmx_request) {
                $target_edge = 'layout-view';
            }
            // Otherwise, keep the default layout-main set at the beginning of the method
        }

        print_rr($endpoint, "endpoint");
       
       
        if ($route_error && isset($_SERVER['HTTP_HX_REQUEST'])) {
            // For HTMX requests, return a simple error message without reloading the whole app
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . addslashes($error_message) . '"}}');
            return "<div class='p-4 bg-red-100 border border-red-400 text-red-700 rounded'>
                    <h3 class='font-bold'>Error</h3>
                    <p>{$error_message}</p>
                </div>";
        }

        // If there's a routing error but not an HTMX request, continue with default behavior
        // but log the error
        if ($route_error) {
            // For non-HTMX requests or if we want to show the error in the main layout
            $error_content = "<div class='p-4 bg-red-100 border border-red-400 text-red-700 rounded'>
                <h3 class='font-bold'>Error</h3>
                <p>{$error_message}</p>
            </div>";

            // Return the error in the main layout
            return Edge::render($target_edge, ['view_content' => $error_content]);
        }

        // Load function files using the autoloader
        autobooks_load_function_file($current_page);
        autobooks_load_function_file(str_replace(['.view.','.api.'], '.fn.', $endpoint));

        // Load function files for all parts of the path
        autobooks_load_path_functions(PATH_PARTS, $current_page);

        // Add this code to load system function files if needed
        if (strpos($app_path, 'system') === 0 || TOP_LEVEL === 'system') {
            // Try to load system function files
            $system_page = str_replace('system/', '', $app_path);
            if (empty($system_page)) $system_page = $current_page;
            
            // Load system function file
            autobooks_load_function_file('system/' . $system_page);
        }

        if (!$public) self::checkPermissions($current_page);

        print_rr($endpoint, "launching $target_edge with");

        echo "<!-- launching $target_edge with $endpoint-->";
        // Check if the view file has .edge.php extension (for edge template views)
        if (file_exists($endpoint)){
            if (str_ends_with($endpoint, '.edge.php')) {
                print_rr(['endpoint' => $endpoint, 'function_call' => $function_call],"edge view found");
                // For edge views, render the view content and pass it to the layout
                $rendered_view_content = Edge::renderView($endpoint, ['function_call' => $function_call]);
                return Edge::render($target_edge, [
                    'view_content' => $rendered_view_content,
                    'view' => $endpoint, // Also pass the view path for compatibility
                    'function_call' => $function_call
                ]);
            } else {
                print_rr("regular view found");
                // For regular views, use the standard include method
                return Edge::render($target_edge, ['view' => $endpoint, 'function_call' => $function_call]);
            }
        }else{
                tcs_log("View file not found: {$view}", 'router_errors');
                return '
                    <div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        <h3 class="font-bold">Error</h3>
                        <p>View file not found: ' . $endpoint  . ' </p>
                    </div>';
        }
    }

    public static function get_route($app_path = '')    {
        $current_page = CURRENT_PAGE;
        echo "<!-- get_route for {$app_path}/{$current_page} -->";
        // If app_path is empty, default to dashboard
        if (empty($app_path)) {
            $app_path = 'dashboard';
        }

        // First, try to find the route in the database using the new file_path and is_system columns
        $route_info = self::get_route_from_database($app_path, $current_page);
        if ($route_info['status'] === 'success') {
            return $route_info;
        }
        print_rr($route_info, "database route not found");

        // Fallback to legacy logic for backward compatibility
        return self::get_route_legacy($app_path, $current_page);
    }

    /**
     * Get route information from database using file_path and is_system columns
     */
    private static function get_route_from_database($app_path, $current_page) {
        try {
            // Build the route path from app_path
            $route_parts = array_filter(explode('/', trim($app_path, '/')));


            // Try to find the navigation entry
            // First try exact match with the full path
            $route_key = $current_page;
            $parent_path = count($route_parts) > 0 ? implode('/', $route_parts) : '';

            $nav_entry = \system\database::table('autobooks_navigation')
                ->select(['file_path', 'is_system', 'route_key'])
                ->where('route_key', $route_key)
                ->where('parent_path', $parent_path)
                ->first();

            if (!$nav_entry) {
                // Try with root parent path
                $nav_entry = \system\database::table('autobooks_navigation')
                    ->select(['file_path', 'is_system', 'route_key'])
                    ->where('route_key', $route_key)
                    ->where('parent_path', 'root')
                    ->first();
            }

            if ($nav_entry) {
                // Determine the views directory based on is_system flag
                $views_dir = $nav_entry['is_system'] ? FS_SYS_VIEWS : FS_VIEWS;

                // Build file path: <viewsdir>/<file_path>/<route_key>.<edge|view>.php
                $base_path_file = tcs_path($views_dir,$nav_entry['file_path'],$nav_entry['route_key']);
                $base_path_dir = tcs_path($views_dir,$nav_entry['file_path'],$nav_entry['route_key'],$nav_entry['route_key']);

                $file_list = [
                    "{$base_path_file}.view.php",
                    "{$base_path_file}.edge.php",
                    "{$base_path_dir}.view.php",
                    "{$base_path_dir}.edge.php"
                ];

                foreach ($file_list as $file) {
                    $file = tcs_path($file);
                    print_rr($file, "checking database route file");
                    if (file_exists($file)) {
                        return [
                            'status' => 'success',
                            'path' => $file
                        ];
                    }
                }

            }
            print_rr(
                [
                    'route_parts' => $route_parts,
                    'current_page' => $current_page,
                    'file_list' => $file_list,
                    'base_path' => [
                        'views_dir' => $views_dir,
                        'file_path' => $nav_entry['file_path'],
                        'route_key' => $nav_entry['route_key']
                    ]
                ],
                "Database route not found for"
            );
            return [
                'status' => 'error',
                'message' => "Database route not found for: {$app_path}"
            ];

        } catch (\Exception $e) {
            print_rr($e->getMessage(), "database route error");
            return [
                'status' => 'error',
                'message' => "Database error: " . $e->getMessage()
            ];
        }
    }

    /**
     * Legacy route finding logic for backward compatibility
     */
    private static function get_route_legacy($app_path, $current_page) {
        // Check if this is a system view request
        if (strpos($app_path, 'system') === 0 || $app_path === 'system') {
            // Extract the system view name (after 'system/')
            $system_view = str_replace(['system','system/'], '', $app_path);
            if (empty($system_view)) $system_view = $current_page;

            $system_file_list = [
                FS_SYS_VIEWS . "/{$system_view}/{$current_page}.view.php",
                FS_SYS_VIEWS . "/{$system_view}/{$current_page}.edge.php",
                FS_SYS_VIEWS . "/{$system_view}.view.php",
                FS_SYS_VIEWS . "/{$system_view}.edge.php"
            ];
            print_rr($system_file_list, "system file list");
            foreach ($system_file_list as $file) {
                $file = tcs_path($file);
                print_rr($file, "system file");
                if (file_exists($file)) {
                    return [
                        'status' => 'success',
                        'path' => $file
                    ];
                }
            }
        }

        // Regular view file paths (non-system)
        $view_paths = [
            FS_VIEWS . "/{$app_path}",
            FS_SYS_VIEWS . "/{$app_path}"
        ];
        foreach ($view_paths as $view_path) {
            $file_list = [
                "{$view_path}.view.php",
                "{$view_path}.edge.php",
                "{$view_path}/{$current_page}.view.php",
                "{$view_path}/{$current_page}.edge.php",
                "{$view_path}/{$current_page}/{$current_page}.view.php",
                "{$view_path}/{$current_page}/{$current_page}.edge.php"
            ];

        foreach ($file_list as $file) {
            $file = tcs_path($file);
            print_rr($file, "Looking for view file:");
            if (file_exists($file)) {
                return [
                    'status' => 'success',
                    'path' => $file
                ];
            }
        }   }
        print_rr($file_list, "view not found in file list");
        // If we're here, none of the files were found
        $error_message = "View not found: {$app_path}";
        tcs_log($error_message,'router_errors');
        return [
            'status' => 'error',
            'message' => $error_message
        ];
    }




    public static function get_api($app_path = '') {

        $source_page = SOURCE_PAGE;
        $current_page = CURRENT_PAGE;
        $path_parts = PATH_PARTS;

        // Check if SOURCE_APP_PATH_PARTS is defined, if not, create it from available data
        if (!defined('SOURCE_APP_PATH_PARTS')) {
            // Try to get from HTTP_HX_CURRENT_URL if available
            if (isset($_SERVER['HTTP_HX_CURRENT_URL'])) {
                $hx_url_parts = parse_url($_SERVER['HTTP_HX_CURRENT_URL']);
                $source_path_parts = explode('/', trim($hx_url_parts['path'], '/'));
                // Remove app root parts to get relative path
                $app_root_parts = explode('/', trim(APP_ROOT, '/'));
                $source_path_parts = array_slice($source_path_parts, count($app_root_parts));
            } else {
                // Fallback to current path parts
                $source_path_parts = $path_parts;
            }
            define('SOURCE_APP_PATH_PARTS', $source_path_parts);
        }

        $source_path_parts = SOURCE_APP_PATH_PARTS;
        $api_path = ($path_parts[1] == 'system' || $source_path_parts[0] == 'system') ? FS_SYS_API : FS_API;

        // Remove 'api' from the start of path parts if it exists
        if ($path_parts[0] === 'api') {
            array_shift($path_parts);
        }
        if ($path_parts[0] === 'system') {
            array_shift($path_parts);
        }
        // If we have a directory structure in the URL
        if (count($path_parts) > 1) {
            $action = array_pop($path_parts); // Remove the function call from the path
            $api_directory = tcs_path($path_parts);  // rest is the directory/module name


            // Look for <directory>.api.php in the api folder
            $locations = [
                'app' => tcs_path(FS_API,"{$api_directory}.api.php"),
                'system' => tcs_path(FS_SYS_API,"{$api_directory}.api.php")
            ];
            foreach ($locations as $api_file) {
                print_rr("Looking for API file: {$api_file}");
                if (file_exists($api_file)) {
                    print_rr("API file found: {$api_file}");
                    return [
                        'status' => 'success',
                        'path' => $api_file
                    ];
                }
            }


            $error_message = "API file not found: {$api_file}";
            print_rr($error_message);
            tcs_log($error_message,'router_errors');
            return [
                'status' => 'error',
                'message' => $error_message
            ];

        } else {
            // Handle original source page based routing
            $api_result = self::handle_source_page_routing($source_page, $current_page,$path_parts);

            if (is_array($api_result)) {
                return $api_result; // Already in the new format
            } else if ($api_result && file_exists($api_result)) {
                return [
                    'status' => 'success',
                    'path' => $api_result
                ];
            } else {
                $error_message = "API endpoint not found for {$source_page}";
                tcs_log($error_message,'router_errors');
                return [
                    'status' => 'error',
                    'message' => $error_message
                ];
            }
        }
    }

// Helper function to handle the original routing logic
    public static  function handle_source_page_routing($source_page, $current_page)    {
        // First try database-driven API routing
        $db_api_result = self::get_api_from_database($source_page, $current_page);
        if ($db_api_result['status'] === 'success') {
            return $db_api_result;
        }

        // Fallback to legacy API routing
        $view_path = in_array($source_page, SYSTEM_VIEWS) ? FS_SYS_VIEWS : FS_VIEWS;
        $file_name = $source_page . '.api.php';
        $api_path = '/' . tcs_path($view_path . '/' . SOURCE_APP_PATH . '/');
        if (file_exists($api_path . '/' . $file_name)) {
            return [
                'status' => 'success',
                'path' => $api_path . '/' . $file_name
            ];
        }
        $view_path = in_array(SOURCE_APP_PATH_PARTS[0], SYSTEM_VIEWS) ? FS_SYS_VIEWS : FS_VIEWS;
        $api_path = '/' . tcs_path($view_path . '/' . SOURCE_APP_PATH . '/' . $source_page);
        print_rr("Looking for API file: {$api_path}/{$file_name}");
        if (file_exists($api_path . '/' . $file_name)) {
            return [
                'status' => 'success',
                'path' => $api_path . '/' . $file_name
            ];
        }

        $error_message = "API file not found in source page paths: {$api_path}/{$file_name} ";
        tcs_log($error_message,'router_errors');
        return [
            'status' => 'error',
            'message' => $error_message
        ];
    }

    /**
     * Get API file path from database using file_path and is_system columns
     */
    private static function get_api_from_database($source_page, $current_page) {
        try {
            // Build the route path from SOURCE_APP_PATH
            $source_app_path = defined('SOURCE_APP_PATH') ? SOURCE_APP_PATH : '';
            $route_parts = SOURCE_APP_PATH_PARTS;

            if (empty($route_parts)) {
                return [
                    'status' => 'error',
                    'message' => "No route parts found for API lookup"
                ];
            }

            // Try to find the navigation entry
            $route_key = $source_page; // Last part is the route key
            $parent_path = count($route_parts) > 0 ? $route_parts[count($route_parts) - 1] : 'root';

            $nav_entry = \system\database::table('autobooks_navigation')
                ->select(['file_path', 'is_system', 'route_key'])
                ->where('route_key', $route_key)
                ->where('parent_path', $parent_path)
                ->first();

            print_rr([
                'route_parts' => $route_parts,
                'route_key' => $route_key,
                'parent_path' => $parent_path,
                'nav_entry' => $nav_entry
            ], "Database API route not found for");

            if (!$nav_entry) {
                // Try with root parent path
                $nav_entry = \system\database::table('autobooks_navigation')
                    ->select(['file_path', 'is_system', 'route_key'])
                    ->where('route_key', $route_key)
                    ->where('parent_path', 'root')
                    ->first();
            }

            if ($nav_entry) {
                // Determine the views directory based on is_system flag
                $views_dir = $nav_entry['is_system'] ? FS_SYS_VIEWS : FS_VIEWS;

                // Build API file path: <viewsdir>/<file_path>/<route_key>/<source_page>.api.php
                $api_file_path = $views_dir . '/' . trim($nav_entry['file_path'], '/') . '/' . $nav_entry['route_key'] . '/' . $source_page . '.api.php';

                $api_file_path = tcs_path($api_file_path);
                print_rr($api_file_path, "checking database API file");
                if (file_exists($api_file_path)) {
                    return [
                        'status' => 'success',
                        'path' => $api_file_path
                    ];
                }
            }

            return [
                'status' => 'error',
                'message' => "Database API route not found for: {$source_app_path}/{$source_page}"
            ];

        } catch (\Exception $e) {
            print_rr($e->getMessage(), "database API route error");
            return [
                'status' => 'error',
                'message' => "Database API error: " . $e->getMessage()
            ];
        }
    }

    public static function register($route, $controller) {
        self::$routes[$route] = $controller;
    }

    /**
     * Log an error and create a notification
     *
     * @param string $message Error message to log
     * @param string $level Error level (error, warning, info)
     * @return void
     */
    public static function logError($message, $level = 'error') {
        // Log to error log
        error_log("[Router {$level}] {$message}");

        // Log to application log if the function exists
        if (function_exists('tcs_log')) {
            tcs_log($message, 'router_errors');
        }

        // Create a notification if the class exists
        if (class_exists('\Notification\Notification')) {
            try {
                $notification = new \Notification\Notification();
                $notification->create([
                    'type' => 'system_error',
                    'title' => 'Routing Error',
                    'message' => $message,
                    'level' => $level,
                    'context' => [
                        'url' => $_SERVER['REQUEST_URI'],
                        'timestamp' => date('Y-m-d H:i:s')
                    ]
                ]);
            } catch (\Exception $e) {
                error_log("Failed to create notification: " . $e->getMessage());
            }
        }
    }

    public static function checkPermissions($route) {
        $permissions = include('system/route_permissions.php');
        $required_role = $permissions['routes'][$route] ?? $permissions['default'];
        users::requireRole($required_role);
    }
}