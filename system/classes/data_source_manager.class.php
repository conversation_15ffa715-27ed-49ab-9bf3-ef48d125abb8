<?php
namespace system;

use system\database;
use system\Schema;
use PDO;
use Exception;

/**
 * Data Source Manager Class
 * 
 * Manages database data sources for tables and email campaigns
 * Provides UI-driven configuration and data retrieval
 */
class data_source_manager {
    
    private static $log_target = "data_source_manager";
    
    /**
     * Get all available database tables that can be used as data sources
     * 
     * @return array List of available tables with metadata
     */
    public static function get_available_tables(): array {
        try {
            // Get all tables in the database
            $tables_query = "SHOW TABLES";
            $stmt = database::rawQuery($tables_query);
            $all_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $available_tables = [];
            
            foreach ($all_tables as $table_name) {
                // Skip system tables and temporary tables
                if (strpos($table_name, 'temp_') === 0 ||
                    strpos($table_name, 'cache_') === 0 ||
                    in_array($table_name, ['sessions', 'migrations', 'information_schema', 'mysql', 'performance_schema', 'sys'])) {
                    continue;
                }

                // Get table info with error handling
                try {
                    $table_info = self::get_table_info($table_name);
                    if ($table_info) {
                        $available_tables[] = $table_info;
                    }
                } catch (Exception $e) {
                    tcs_log("Error getting info for table $table_name: " . $e->getMessage(), self::$log_target);
                    // Continue with other tables
                    continue;
                }
            }
            
            // Sort by table name
            usort($available_tables, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            return $available_tables;
            
        } catch (Exception $e) {
            tcs_log("Error getting available tables: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Get detailed information about a specific table
     * 
     * @param string $table_name Table name
     * @return array|null Table information
     */
    public static function get_table_info(string $table_name): ?array {
        try {
            // Get column information
            $columns_query = "DESCRIBE `$table_name`";
            $stmt = database::rawQuery($columns_query);
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get row count
            $count_query = "SELECT COUNT(*) as row_count FROM `$table_name`";
            $stmt = database::rawQuery($count_query);
            $row_count = $stmt->fetch(PDO::FETCH_ASSOC)['row_count'];

            // Determine table type and category
            $category = self::categorize_table($table_name, $columns);

            return [
                'name' => $table_name,
                'display_name' => self::format_table_display_name($table_name),
                'category' => $category,
                'row_count' => (int)$row_count,
                'columns' => $columns,
                'primary_key' => self::get_primary_key($columns),
                'has_data_json' => self::has_data_json_column($columns),
                'created_at' => self::get_created_at_column($columns),
                'description' => self::generate_table_description($table_name, $columns, $row_count)
            ];

        } catch (Exception $e) {
            tcs_log("Error getting table info for $table_name: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }
    
    /**
     * Get sample data from a table for preview
     * 
     * @param string $table_name Table name
     * @param int $limit Number of rows to return
     * @return array Sample data
     */
    public static function get_sample_data(string $table_name, int $limit = 5): array {
        try {
            $db = database::table($table_name);
            $data = $db->limit($limit)->get();

            return [
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ];
            
        } catch (Exception $e) {
            tcs_log("Error getting sample data for $table_name: " . $e->getMessage(), self::$log_target);
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [],
                'count' => 0
            ];
        }
    }
    
    /**
     * Create a new data source configuration
     * 
     * @param array $config Data source configuration
     * @return int Data source ID
     */
    public static function create_data_source(array $config): int {
        try {
            // Ensure data source table exists
            self::ensure_data_source_table();
            
            $defaults = [
                'status' => 'active',
                'created_by' => $_SESSION['user_id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $data_source = array_merge($defaults, $config);

            // Validate required fields
            if (empty($data_source['name']) || empty($data_source['table_name'])) {
                throw new Exception('Data source name and table name are required');
            }

            // Auto-generate column aliases for unified field mapping
            if (($data_source['mapping_method'] ?? '') === 'unified_field_mapper' &&
                !empty($data_source['unified_mappings']) &&
                empty($data_source['column_aliases'])) {

                tcs_log("Auto-generating column aliases for unified field mapping", self::$log_target);

                // Generate aliases for each table
                $column_aliases = [];
                $tables = $data_source['resolved_tables'] ?? $data_source['tables'] ?? [$data_source['table_name']];

                foreach ($tables as $table_name) {
                    $table_aliases = self::generate_unified_field_aliases($table_name, $data_source['unified_mappings']);

                    // Merge the aliases (they already have the correct format from generate_unified_field_aliases)
                    $column_aliases = array_merge($column_aliases, $table_aliases);
                }

                if (!empty($column_aliases)) {
                    $data_source['column_aliases'] = $column_aliases;
                    tcs_log("Generated " . count($column_aliases) . " column aliases: " . json_encode($column_aliases), self::$log_target);
                }
            }
            
            // Convert arrays to JSON
            if (isset($data_source['column_mapping']) && is_array($data_source['column_mapping'])) {
                $data_source['column_mapping'] = json_encode($data_source['column_mapping']);
            }

            if (isset($data_source['filters']) && is_array($data_source['filters'])) {
                $data_source['filters'] = json_encode($data_source['filters']);
            }

            if (isset($data_source['tables']) && is_array($data_source['tables'])) {
                $data_source['tables'] = json_encode($data_source['tables']);
            }

            if (isset($data_source['joins']) && is_array($data_source['joins'])) {
                $data_source['joins'] = json_encode($data_source['joins']);
            }

            if (isset($data_source['selected_columns']) && is_array($data_source['selected_columns'])) {
                $data_source['selected_columns'] = json_encode($data_source['selected_columns']);
            }

            if (isset($data_source['table_aliases']) && is_array($data_source['table_aliases'])) {
                $data_source['table_aliases'] = json_encode($data_source['table_aliases']);
            }

            if (isset($data_source['column_aliases']) && is_array($data_source['column_aliases'])) {
                $data_source['column_aliases'] = json_encode($data_source['column_aliases']);
            }

            if (isset($data_source['custom_columns']) && is_array($data_source['custom_columns'])) {
                $data_source['custom_columns'] = json_encode($data_source['custom_columns']);
            }

            if (isset($data_source['sorting']) && is_array($data_source['sorting'])) {
                $data_source['sorting'] = json_encode($data_source['sorting']);
            }

            if (isset($data_source['grouping']) && is_array($data_source['grouping'])) {
                $data_source['grouping'] = json_encode($data_source['grouping']);
            }

            if (isset($data_source['limits']) && is_array($data_source['limits'])) {
                $data_source['limits'] = json_encode($data_source['limits']);
            }

            if (isset($data_source['custom_tables']) && is_array($data_source['custom_tables'])) {
                $data_source['custom_tables'] = json_encode($data_source['custom_tables']);
            }

            // JSON encode multi-table merger fields
            if (isset($data_source['table_patterns']) && is_array($data_source['table_patterns'])) {
                $data_source['table_patterns'] = json_encode($data_source['table_patterns']);
            }

            if (isset($data_source['explicit_tables']) && is_array($data_source['explicit_tables'])) {
                $data_source['explicit_tables'] = json_encode($data_source['explicit_tables']);
            }

            if (isset($data_source['resolved_tables']) && is_array($data_source['resolved_tables'])) {
                $data_source['resolved_tables'] = json_encode($data_source['resolved_tables']);
            }

            if (isset($data_source['column_mappings']) && is_array($data_source['column_mappings'])) {
                $data_source['column_mappings'] = json_encode($data_source['column_mappings']);
            }

            if (isset($data_source['unified_mappings']) && is_array($data_source['unified_mappings'])) {
                $data_source['unified_mappings'] = json_encode($data_source['unified_mappings']);
            }

            $db = database::table('autobooks_data_sources');
            $db->insert($data_source);

            // Get the last inserted ID
            $new_id = database::insertId();

            tcs_log("Created data source: " . $data_source['name'] . " with ID: " . $new_id, self::$log_target);
            return (int)$new_id;
            
        } catch (Exception $e) {
            tcs_log("Error creating data source: " . $e->getMessage(), self::$log_target);
            throw $e;
        }
    }

    /**
     * Generate column aliases based on unified field mappings
     *
     * @param string $table_name Table name to analyze
     * @param array $unified_mappings Unified field mappings configuration
     * @return array Column aliases in format ['original_column' => 'selected_unified_field']
     */
    public static function generate_unified_field_aliases(string $table_name, array $unified_mappings = []): array {
        try {
            $aliases = [];

            // Get table columns
            $table_info = self::get_table_info($table_name);
            if (!$table_info || empty($table_info['columns'])) {
                return $aliases;
            }

            $column_names = array_column($table_info['columns'], 'Field');

            // Get field mapping suggestions
            $suggestions = \system\unified_field_mapper::suggest_field_mappings($column_names);

            $min_confidence = $unified_mappings['min_confidence'] ?? 75;
            $manual_overrides = $unified_mappings['overrides'] ?? [];

            foreach ($suggestions as $original_column => $suggestion) {
                // Check for manual override first
                if (isset($manual_overrides[$original_column])) {
                    $selected_field = $manual_overrides[$original_column]['field_name'] ?? $suggestion['field_name'];
                } else {
                    // Use automatic suggestion if confidence is high enough
                    if ($suggestion['confidence'] >= $min_confidence) {
                        $selected_field = $suggestion['field_name'];
                    } else {
                        continue; // Skip low confidence matches
                    }
                }

                // Create alias in the format: table.column => column_AS_selected_field
                $column_key = $table_name . '.' . $original_column;
                $alias_value = $selected_field;
                $aliases[$column_key] = $alias_value;

                tcs_log("Generated alias: {$column_key} => {$alias_value}", self::$log_target);
            }

            return $aliases;

        } catch (Exception $e) {
            tcs_log("Error generating unified field aliases for {$table_name}: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Get all configured data sources
     *
     * @param array $criteria Optional filtering criteria
     * @return array List of data sources
     */
    public static function get_data_sources(array $criteria = []): array {
        try {
            self::ensure_data_source_table();

            $db = database::table('autobooks_data_sources');

            // Apply campaign filtering if specified
            if (!empty($criteria['campaign_id'])) {
                // For now, we'll get the campaign's assigned data source
                // In the future, this could be expanded to support multiple data sources per campaign
                $campaign_data_source = database::table('email_campaigns')
                    ->where('id', $criteria['campaign_id'])
                    ->value('data_source_id');

                if ($campaign_data_source) {
                    $db->where('id', $campaign_data_source);
                } else {
                    // If no data source is assigned to the campaign, return all sources
                    // This allows for initial selection during campaign setup
                }
            }

            $sources = $db->orderBy('name')->get();
            
            // Decode JSON fields
            foreach ($sources as &$source) {
                if (!empty($source['column_mapping'])) {
                    $source['column_mapping'] = json_decode($source['column_mapping'], true);
                }
                if (!empty($source['filters'])) {
                    $source['filters'] = json_decode($source['filters'], true);
                }
                if (!empty($source['tables'])) {
                    $source['tables'] = json_decode($source['tables'], true);
                }
                if (!empty($source['joins'])) {
                    $source['joins'] = json_decode($source['joins'], true);
                }
                if (!empty($source['selected_columns'])) {
                    $source['selected_columns'] = json_decode($source['selected_columns'], true);
                }
                if (!empty($source['table_aliases'])) {
                    $source['table_aliases'] = json_decode($source['table_aliases'], true);
                }
                if (!empty($source['column_aliases'])) {
                    $source['column_aliases'] = json_decode($source['column_aliases'], true);
                }
                if (!empty($source['custom_columns'])) {
                    $source['custom_columns'] = json_decode($source['custom_columns'], true);
                }
                if (!empty($source['sorting'])) {
                    $source['sorting'] = json_decode($source['sorting'], true);
                }
                if (!empty($source['grouping'])) {
                    $source['grouping'] = json_decode($source['grouping'], true);
                }
                if (!empty($source['limits'])) {
                    $source['limits'] = json_decode($source['limits'], true);
                }
            }
            
            return $sources;
            
        } catch (Exception $e) {
            tcs_log("Error getting data sources: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Get data from a configured data source with full support for sorting, filtering, searching, and pagination
     *
     * @param int $data_source_id Data source ID
     * @param array $criteria Additional criteria (search, limit, pagination, sorting, filtering, etc.)
     * @return array Data results
     */
    public static function get_data_source_data(int $data_source_id, array $criteria = []): array {
        try {
            // Get data source configuration
            $data_source = self::get_data_source($data_source_id);
            if (!$data_source) {
                throw new Exception("Data source {$data_source_id} not found");
            }

            // Use the enhanced build_multi_table_query function for proper support of all features
            require_once 'system/api/data_sources.api.php';

            // Prepare data for build_multi_table_query
            $tables = $data_source['tables'] ?? [$data_source['table_name']];
            $joins = $data_source['joins'] ?? [];
            $selected_columns = $data_source['selected_columns'] ?? [];
            $filters = $data_source['filters'] ?? [];
            $table_aliases = $data_source['table_aliases'] ?? [];
            $column_aliases = $data_source['column_aliases'] ?? [];
            $custom_columns = $data_source['custom_columns'] ?? [];
            $custom_tables = $data_source['custom_tables'] ?? [];
            $sorting = $data_source['sorting'] ?? [];
            $grouping = $data_source['grouping'] ?? [];
            $limits = $data_source['limits'] ?? [];

            // Get primary table for search column mapping
            $primary_table = $data_source['table_name'];

            // Handle pagination from criteria
            if (!empty($criteria['limit'])) {
                $limits['enabled'] = true;
                $limits['limit'] = (int)$criteria['limit'];

                // Handle offset directly or calculate from page
                if (!empty($criteria['offset'])) {
                    $limits['offset'] = (int)$criteria['offset'];
                } elseif (!empty($criteria['page'])) {
                    $page = (int)$criteria['page'];
                    $limits['offset'] = ($page - 1) * $limits['limit'];
                }
            }

            // Handle per_page parameter (common in data table APIs)
            if (!empty($criteria['per_page'])) {
                $limits['enabled'] = true;
                $limits['limit'] = (int)$criteria['per_page'];

                $page = (int)($criteria['page'] ?? 1);
                $limits['offset'] = ($page - 1) * $limits['limit'];
            }

            // Handle sorting from criteria (multiple possible parameter names)
            $sort_column = $criteria['sort_column'] ?? $criteria['order_by'] ?? null;
            $sort_direction = $criteria['sort_direction'] ?? $criteria['order_direction'] ?? 'ASC';

            if (!empty($sort_column)) {
                $additional_sort = [
                    'column' => $sort_column,
                    'direction' => strtoupper($sort_direction)
                ];
                // Add to beginning of sorting array to give it priority over data source defaults
                array_unshift($sorting, $additional_sort);
            }

            // Handle search criteria (supports both 'search' and 'search_terms' parameters)
            $search_term = $criteria['search'] ?? $criteria['search_terms'] ?? null;
            if (!empty($search_term)) {
                $search_columns = self::get_searchable_columns_for_data_source($data_source, $selected_columns);

                // Ensure search_columns is always an array to prevent count() errors
                if (!is_array($search_columns)) {
                    $search_columns = [];
                }

                tcs_log("Search term: '$search_term', Data source ID: {$data_source['id']}, Search columns: " . json_encode($search_columns), self::$log_target);
                print_rr($search_columns, 'search columnsz');

                // Add search filters for each search column
                $search_conditions = [];
                foreach ($search_columns as $search_column) {
                    $search_conditions[] = [
                        'column' => $search_column,
                        'operator' => 'LIKE',
                        'value' => '%' . $search_term . '%'
                    ];
                }

                // Add as OR conditions if we have search columns
                if (!empty($search_conditions)) {
                    $filters[] = [
                        'type' => 'OR',
                        'conditions' => $search_conditions
                    ];
                } else {
                    tcs_log("No search conditions created - no searchable columns found for data source {$data_source['id']}", self::$log_target);
                }
            }

            // Handle column-specific filters from data table
            if (!empty($criteria['where']) && is_array($criteria['where'])) {
                foreach ($criteria['where'] as $column => $condition) {
                    if (is_array($condition) && count($condition) >= 2) {
                        $operator = $condition[0];
                        $value = $condition[1];
                        $filters[] = [
                            'type' => 'AND',
                            'conditions' => [[
                                'column' => $column,
                                'operator' => $operator,
                                'value' => $value
                            ]]
                        ];
                    } else {
                        // Simple equality filter
                        $filters[] = [
                            'type' => 'AND',
                            'conditions' => [[
                                'column' => $column,
                                'operator' => '=',
                                'value' => $condition
                            ]]
                        ];
                    }
                }
            }

            // Handle individual column filters (from data table column filters)
            if (!empty($criteria['column']) && is_array($criteria['column'])) {
                foreach ($criteria['column'] as $column => $value) {
                    if (!empty($value)) {
                        // Parse column name (format: table_column)
                        $col_parts = explode("_", $column, 2);
                        if (count($col_parts) === 2) {
                            $table = $col_parts[0];
                            $column_name = $col_parts[1];
                            $full_column = "{$table}.{$column_name}";
                        } else {
                            $full_column = $column;
                        }

                        $filters[] = [
                            'type' => 'AND',
                            'conditions' => [[
                                'column' => $full_column,
                                'operator' => '=',
                                'value' => $value
                            ]]
                        ];
                    }
                }
            }

            // Build and execute the query using our enhanced function
            $query = \api\data_sources\build_multi_table_query(
                $tables,
                $joins,
                $selected_columns,
                $filters,
                $table_aliases,
                $column_aliases,
                $custom_columns,
                $sorting,
                $grouping,
                $limits,
                $custom_tables
            );

            // Execute the raw query with error handling
            try {
                tcs_log("Executing query: " . $query, self::$log_target);
                $stmt = database::rawQuery($query);
                $data = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            } catch (Exception $query_error) {
                tcs_log("Data source query failed: " . $query_error->getMessage(), self::$log_target);
                tcs_log("Failed query: " . $query, self::$log_target);
                throw new Exception("Data source query failed: " . $query_error->getMessage());
            }

            // Get total count for pagination (if limits are applied)
            $total_count = count($data);
            if (!empty($limits['enabled'])) {
                try {
                    // Execute count query without limits
                    $count_query = \api\data_sources\build_multi_table_query(
                        $tables,
                        $joins,
                        ['COUNT(*) as total'], // Just count
                        $filters, // Same filters
                        $table_aliases,
                        $column_aliases,
                        $custom_columns,
                        [], // No sorting for count
                        $grouping,
                        [], // No limits for count
                        $custom_tables
                    );

                    $count_stmt = database::rawQuery($count_query);
                    $count_result = $count_stmt->fetch(\PDO::FETCH_ASSOC);
                    $total_count = (int)($count_result['total'] ?? 0);
                } catch (Exception $count_error) {
                    // If count query fails, use the current data count as fallback
                    tcs_log("Count query failed: " . $count_error->getMessage(), self::$log_target);
                    $total_count = count($data);
                }
            }
            // Generate columns safely - handle empty data
            $columns = [];
            if (!empty($data) && is_array($data) && isset($data[0]) && is_array($data[0])) {
                $column_names = array_keys($data[0]);
                foreach ($column_names as $column) {
                    $columns[] = [
                        'label' => ucwords(str_replace('_', ' ', $column)),
                        'field' => $column,
                        'filter' => true
                    ];
                }
            }

            print_rr([
            'success' => true,
                'data' => $data,
                'columns' => $columns,
                'count' => count($data), // Current page count
                'total_count' => $total_count, // Total records (for pagination)
                'data_source' => $data_source,
                'criteria' => $criteria, // Include criteria for debugging
                'query' => $query // Include query for debugging
            ], 'columnsy');
            return [
                'success' => true,
                'data' => $data,
                'columns' => $columns,
                'count' => count($data), // Current page count
                'total_count' => $total_count, // Total records (for pagination)
                'data_source' => $data_source,
                'criteria' => $criteria, // Include criteria for debugging
                'query' => $query // Include query for debugging
            ];

        } catch (Exception $e) {
            tcs_log("Error getting data source data: " . $e->getMessage(), self::$log_target);
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [],
                'count' => 0,
                'total_count' => 0
            ];
        }
    }

    /**
     * Get searchable columns for a data source
     *
     * @param array $data_source Data source configuration
     * @param array $selected_columns Selected columns from data source
     * @return array Array of searchable column names
     */
    private static function get_searchable_columns_for_data_source(array $data_source, array $selected_columns): array {
        $search_columns = [];
        $primary_table = $data_source['table_name'];
        $joins = $data_source['joins'] ?? [];

        tcs_log("Getting searchable columns for data source {$data_source['id']}, table: $primary_table", self::$log_target);

        // If specific search columns are configured in the data source, use those
        if (!empty($data_source['search_columns'])) {
            $configured_columns = $data_source['search_columns'];
            // Ensure it's an array (could be JSON string)
            if (is_string($configured_columns)) {
                $configured_columns = json_decode($configured_columns, true) ?? [];
            }
            if (is_array($configured_columns)) {
                tcs_log("Using configured search columns: " . json_encode($configured_columns), self::$log_target);
                return $configured_columns;
            }
        }

        // Auto-detect searchable columns based on selected columns and table structure
        if (!empty($selected_columns)) {
            foreach ($selected_columns as $table => $columns) {
                $current_table = $table;
                foreach ($columns as $column) {
                    if ($column && is_string($column) ) {
                         $search_columns[] = $current_table . '.' . $column;
                    }
                }
//                // Handle different possible column structures
//                if (is_string($column)) {
//                    $column_name = $column;
//                } elseif (is_array($column)) {
//                    $column_name = $column['column'] ?? $column['name'] ?? $column['field'] ?? null;
//                }
//
//                // Include text-based columns for searching
//
            }
        }

        // If no selected columns or no searchable columns found, use table-specific defaults
        if (empty($search_columns)) {
            $search_columns = self::get_default_search_columns($primary_table, $joins);
        }

        // Ensure we always return an array
        if (!is_array($search_columns)) {
            $search_columns = [];
        }

        tcs_log("Final search columns for $primary_table: " . json_encode($search_columns), self::$log_target);
        return $search_columns;
    }

    /**
     * Check if a column is suitable for searching (text-based columns)
     *
     * @param string $column_name Column name
     * @return bool True if column is searchable
     */
    private static function is_searchable_column(string $column_name): bool {
        // Common searchable column patterns
        $searchable_patterns = [
            'name', 'title', 'description', 'email', 'subject', 'content',
            'text', 'comment', 'note', 'address', 'city', 'company',
            'offering', 'subscription', 'product', 'reference', 'number'
        ];

        $lower_column = strtolower($column_name);

        foreach ($searchable_patterns as $pattern) {
            if (strpos($lower_column, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get default search columns for specific tables
     *
     * @param string $primary_table Primary table name
     * @param array $joins Join configuration
     * @return array Default search columns
     */
    private static function get_default_search_columns(string $primary_table, array $joins): array {
        $search_columns = [];

        // Table-specific search column mappings
        switch ($primary_table) {
            case 'autodesk_subscriptions':
                $search_columns = ['autodesk_subscriptions.subscriptionId', 'autodesk_subscriptions.offeringName'];
                // Add customer name if joined
                if (!empty($joins)) {
                    foreach ($joins as $join) {
                        if (strpos($join['table'] ?? '', 'accounts') !== false) {
                            $alias = $join['alias'] ?? $join['table'];
                            $search_columns[] = $alias . '.name';
                            break;
                        }
                    }
                }
                break;

            case 'autodesk_accounts':
                $search_columns = ['autodesk_accounts.name', 'autodesk_accounts.email'];
                break;

            case 'autodesk_email_history':
                $search_columns = ['autodesk_email_history.recipient_email', 'autodesk_email_history.subject'];
                break;

            default:
                // Generic fallback - try common column names
                $search_columns = [
                    $primary_table . '.name',
                    $primary_table . '.title',
                    $primary_table . '.description'
                ];
                break;
        }

        return $search_columns;
    }

    /**
     * Duplicate an existing data source
     *
     * @param int $source_id ID of the data source to duplicate
     * @param string $new_name Optional new name for the duplicated source
     * @return int New data source ID
     */
    public static function duplicate_data_source(int $source_id, string $new_name = ''): int {
        try {
            // Get the original data source
            $original_source = self::get_data_source($source_id);
            if (!$original_source) {
                throw new Exception('Original data source not found');
            }

            // Prepare the new data source configuration
            $new_config = $original_source;

            // Remove ID and update metadata
            unset($new_config['id']);
            $new_config['name'] = !empty($new_name) ? $new_name : 'Copy of ' . $original_source['name'];
            $new_config['created_by'] = $_SESSION['user_id'] ?? null;
            $new_config['created_at'] = date('Y-m-d H:i:s');
            $new_config['updated_at'] = date('Y-m-d H:i:s');

            // Create the new data source
            $new_id = self::create_data_source($new_config);

            tcs_log("Duplicated data source {$source_id} as new ID {$new_id}: " . $new_config['name'], self::$log_target);
            return $new_id;

        } catch (Exception $e) {
            tcs_log("Error duplicating data source {$source_id}: " . $e->getMessage(), self::$log_target);
            throw $e;
        }
    }

    /**
     * Get a specific data source by ID
     *
     * @param int $id Data source ID
     * @return array|null Data source configuration
     */
    public static function get_data_source(int $id): ?array {
        try {
            self::ensure_data_source_table();
            
            $db = database::table('autobooks_data_sources');
            $source = $db->where('id', $id)->first();
            
            if ($source) {
                // Decode JSON fields
                if (!empty($source['column_mapping'])) {
                    $source['column_mapping'] = json_decode($source['column_mapping'], true);
                }
                if (!empty($source['filters'])) {
                    $source['filters'] = json_decode($source['filters'], true);
                }
                if (!empty($source['tables'])) {
                    $source['tables'] = json_decode($source['tables'], true);
                }
                if (!empty($source['joins'])) {
                    $source['joins'] = json_decode($source['joins'], true);
                }
                if (!empty($source['selected_columns'])) {
                    $source['selected_columns'] = json_decode($source['selected_columns'], true);
                }
                if (!empty($source['table_aliases'])) {
                    $source['table_aliases'] = json_decode($source['table_aliases'], true);
                }
                if (!empty($source['column_aliases'])) {
                    $source['column_aliases'] = json_decode($source['column_aliases'], true);
                }
                if (!empty($source['custom_columns'])) {
                    $source['custom_columns'] = json_decode($source['custom_columns'], true);
                }
                if (!empty($source['sorting'])) {
                    $source['sorting'] = json_decode($source['sorting'], true);
                }
                if (!empty($source['grouping'])) {
                    $source['grouping'] = json_decode($source['grouping'], true);
                }
                if (!empty($source['limits'])) {
                    $source['limits'] = json_decode($source['limits'], true);
                }
                if (!empty($source['custom_tables'])) {
                    $source['custom_tables'] = json_decode($source['custom_tables'], true);
                }
                if (!empty($source['table_patterns'])) {
                    $source['table_patterns'] = json_decode($source['table_patterns'], true);
                }
                if (!empty($source['explicit_tables'])) {
                    $source['explicit_tables'] = json_decode($source['explicit_tables'], true);
                }
                if (!empty($source['resolved_tables'])) {
                    $source['resolved_tables'] = json_decode($source['resolved_tables'], true);
                }
                if (!empty($source['column_mappings'])) {
                    $source['column_mappings'] = json_decode($source['column_mappings'], true);
                }
                if (!empty($source['unified_mappings'])) {
                    $source['unified_mappings'] = json_decode($source['unified_mappings'], true);
                }
            }

            return $source;
            
        } catch (Exception $e) {
            tcs_log("Error getting data source $id: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }
    
    // Private helper methods
    
    private static function categorize_table(string $table_name, array $columns): string {
        if (strpos($table_name, 'autobooks_') === 0 || strpos($table_name, 'autobooks_import_') === 0) {
            if (strpos($table_name, '_data') !== false) {
                return 'data_table';
            } elseif (strpos($table_name, 'email') !== false || strpos($table_name, 'campaign') !== false) {
                return 'email';
            } elseif (strpos($table_name, 'user') !== false) {
                return 'users';
            } else {
                return 'system';
            }
        } elseif (strpos($table_name, 'autodesk') === 0) {
            return 'autodesk';
        } else {
            return 'other';
        }
    }
    
    private static function format_table_display_name(string $table_name): string {
        // Remove common prefixes
        $display_name = $table_name; //str_replace(['autobooks_', 'autodesk_'], '', $table_name);
        
        // Replace underscores with spaces and capitalize
        $display_name = ucwords(str_replace('_', ' ', $display_name));
        
        return $display_name;
    }
    
    private static function get_primary_key(array $columns): ?string {
        foreach ($columns as $column) {
            if ($column['Key'] === 'PRI') {
                return $column['Field'];
            }
        }
        return null;
    }
    
    private static function has_data_json_column(array $columns): bool {
        foreach ($columns as $column) {
            if ($column['Field'] === 'data_json') {
                return true;
            }
        }
        return false;
    }
    
    private static function get_created_at_column(array $columns): ?string {
        foreach ($columns as $column) {
            if (in_array($column['Field'], ['created_at', 'date_created', 'timestamp'])) {
                return $column['Field'];
            }
        }
        return null;
    }
    
    private static function generate_table_description(string $table_name, array $columns, int $row_count): string {
        $category = self::categorize_table($table_name, $columns);
        $column_count = count($columns);
        
        $descriptions = [
            'data_table' => "Data table with $column_count columns and $row_count records",
            'email' => "Email/campaign table with $column_count columns and $row_count records", 
            'users' => "User management table with $column_count columns and $row_count records",
            'system' => "System table with $column_count columns and $row_count records",
            'autodesk' => "Autodesk integration table with $column_count columns and $row_count records",
            'other' => "Database table with $column_count columns and $row_count records"
        ];
        
        return $descriptions[$category] ?? $descriptions['other'];
    }
    
    private static function ensure_data_source_table(): void {
        // Check if table exists using raw query for better compatibility
        try {
            $check_query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'";
            $stmt = database::rawQuery($check_query);
            $exists = $stmt->fetchColumn() > 0;

            if (!$exists) {
                // Create table using raw SQL for maximum compatibility
                $create_sql = "
                CREATE TABLE `autobooks_data_sources` (
                    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL COMMENT 'Display name for the data source',
                    `table_name` varchar(255) NOT NULL COMMENT 'Primary database table name',
                    `description` text DEFAULT NULL COMMENT 'Optional description',
                    `category` varchar(50) NOT NULL DEFAULT 'other' COMMENT 'Data source category',
                    `tables` longtext DEFAULT NULL COMMENT 'JSON array of all tables used in this data source',
                    `joins` longtext DEFAULT NULL COMMENT 'JSON array of join configurations',
                    `selected_columns` longtext DEFAULT NULL COMMENT 'JSON array of selected columns from all tables',
                    `table_aliases` longtext DEFAULT NULL COMMENT 'JSON object mapping table names to their aliases',
                    `column_aliases` longtext DEFAULT NULL COMMENT 'JSON object mapping column keys to their aliases',
                    `custom_columns` longtext DEFAULT NULL COMMENT 'JSON array of custom SQL columns with aliases',
                    `custom_tables` longtext DEFAULT NULL COMMENT 'JSON array of custom SQL tables and subqueries with aliases',
                    `sorting` longtext DEFAULT NULL COMMENT 'JSON array of sorting rules (ORDER BY)',
                    `grouping` longtext DEFAULT NULL COMMENT 'JSON array of grouping rules (GROUP BY)',
                    `limits` longtext DEFAULT NULL COMMENT 'JSON object with limit and offset settings',
                    `column_mapping` longtext DEFAULT NULL COMMENT 'JSON column mapping configuration',
                    `filters` longtext DEFAULT NULL COMMENT 'JSON filter configuration',
                    `data_source_type` varchar(50) NOT NULL DEFAULT 'standard' COMMENT 'Type of data source: standard or multi_table_merger',
                    `table_patterns` longtext DEFAULT NULL COMMENT 'JSON array of wildcard patterns for multi-table merger',
                    `explicit_tables` longtext DEFAULT NULL COMMENT 'JSON array of explicitly selected tables for multi-table merger',
                    `resolved_tables` longtext DEFAULT NULL COMMENT 'JSON array of resolved table names from patterns and explicit selection',
                    `mapping_method` varchar(50) DEFAULT NULL COMMENT 'Column mapping method for multi-table merger: like_for_like, manual, unified_field_mapper',
                    `column_mappings` longtext DEFAULT NULL COMMENT 'JSON array of manual column mappings for multi-table merger',
                    `unified_mappings` longtext DEFAULT NULL COMMENT 'JSON object of unified field mapper mappings for multi-table merger',
                    `reference_table` varchar(255) DEFAULT NULL COMMENT 'Reference table name for column mapping assistance',
                    `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT 'Data source status',
                    `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
                    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                    PRIMARY KEY (`id`),
                    KEY `idx_status_category` (`status`,`category`),
                    KEY `idx_table_name` (`table_name`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns with multi-table support'
                ";

                database::rawQuery($create_sql);
                tcs_log("Created autobooks_data_sources table using raw SQL", self::$log_target);
            } else {
                // Table exists, check for missing columns and add them
                self::ensure_data_source_columns();
            }
        } catch (Exception $e) {
            tcs_log("Error ensuring autobooks_data_sources table: " . $e->getMessage(), self::$log_target);
            // Don't throw - let the system continue without the table for now
        }
    }

    /**
     * Ensure all required columns exist in the data source table
     */
    private static function ensure_data_source_columns(): void {
        try {
            // Define required columns with their definitions
            $required_columns = [
                'tables' => 'longtext DEFAULT NULL COMMENT \'JSON array of all tables used in this data source\'',
                'joins' => 'longtext DEFAULT NULL COMMENT \'JSON array of join configurations\'',
                'table_aliases' => 'longtext DEFAULT NULL COMMENT \'JSON object mapping table names to their aliases\'',
                'column_aliases' => 'longtext DEFAULT NULL COMMENT \'JSON object mapping column keys to their aliases\'',
                'custom_columns' => 'longtext DEFAULT NULL COMMENT \'JSON array of custom SQL columns with aliases\'',
                'custom_tables' => 'longtext DEFAULT NULL COMMENT \'JSON array of custom SQL tables and subqueries with aliases\'',
                'sorting' => 'longtext DEFAULT NULL COMMENT \'JSON array of sorting rules (ORDER BY)\'',
                'grouping' => 'longtext DEFAULT NULL COMMENT \'JSON array of grouping rules (GROUP BY)\'',
                'limits' => 'longtext DEFAULT NULL COMMENT \'JSON object with limit and offset settings\'',
                'data_source_type' => 'varchar(50) NOT NULL DEFAULT \'standard\' COMMENT \'Type of data source: standard or multi_table_merger\'',
                'table_patterns' => 'longtext DEFAULT NULL COMMENT \'JSON array of wildcard patterns for multi-table merger\'',
                'explicit_tables' => 'longtext DEFAULT NULL COMMENT \'JSON array of explicitly selected tables for multi-table merger\'',
                'resolved_tables' => 'longtext DEFAULT NULL COMMENT \'JSON array of resolved table names from patterns and explicit selection\'',
                'mapping_method' => 'varchar(50) DEFAULT NULL COMMENT \'Column mapping method for multi-table merger: like_for_like, manual, unified_field_mapper\'',
                'column_mappings' => 'longtext DEFAULT NULL COMMENT \'JSON array of manual column mappings for multi-table merger\'',
                'unified_mappings' => 'longtext DEFAULT NULL COMMENT \'JSON object of unified field mapper mappings for multi-table merger\'',
                'reference_table' => 'varchar(255) DEFAULT NULL COMMENT \'Reference table name for column mapping assistance\''
            ];

            // Check which columns exist
            $existing_columns_query = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'";
            $stmt = database::rawQuery($existing_columns_query);
            $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Add missing columns
            foreach ($required_columns as $column_name => $column_definition) {
                if (!in_array($column_name, $existing_columns)) {
                    $alter_sql = "ALTER TABLE `autobooks_data_sources` ADD COLUMN `{$column_name}` {$column_definition}";
                    database::rawQuery($alter_sql);
                    tcs_log("Added missing column '{$column_name}' to autobooks_data_sources table", self::$log_target);
                }
            }
        } catch (Exception $e) {
            tcs_log("Error ensuring data source columns: " . $e->getMessage(), self::$log_target);
            // Don't throw - let the system continue
        }
    }

    /**
     * Preview multi-table merge data
     *
     * @param array $table_names List of table names to merge
     * @param string $mapping_method Mapping method: 'like_for_like', 'manual', 'unified_field_mapper'
     * @param array $column_mappings Manual column mappings (for manual method)
     * @param array $unified_mappings Unified field mapper mappings (for unified_field_mapper method)
     * @param int $limit Number of rows to return for preview
     * @return array Merged data preview
     */
    public static function preview_multi_table_merge(
        array $table_names,
        string $mapping_method = 'like_for_like',
        array $column_mappings = [],
        array $unified_mappings = [],
        int $limit = 5
    ): array {
        try {
            if (empty($table_names)) {
                return [];
            }

            tcs_log("Previewing multi-table merge for " . count($table_names) . " tables using {$mapping_method} method", self::$log_target);

            // Get sample data from each table
            $table_data = [];
            $all_columns = [];

            foreach ($table_names as $table_name) {
                $sample_result = self::get_sample_data($table_name, $limit);
                if (!empty($sample_result) && $sample_result['success'] && !empty($sample_result['data'])) {
                    $sample_data = $sample_result['data'];
                    $table_data[$table_name] = $sample_data;

                    // Collect all column names
                    foreach ($sample_data as $row) {
                        foreach (array_keys($row) as $column) {
                            $column_key = $table_name . '.' . $column;
                            if (!in_array($column_key, $all_columns)) {
                                $all_columns[] = $column_key;
                            }
                        }
                    }
                }
            }

            if (empty($table_data)) {
                return [];
            }

            // Merge data based on mapping method
            switch ($mapping_method) {
                case 'like_for_like':
                    return self::merge_like_for_like($table_data, $limit);

                case 'manual':
                    return self::merge_manual($table_data, $column_mappings, $limit);

                case 'unified_field_mapper':
                    return self::merge_unified_field_mapper($table_data, $unified_mappings, $limit);

                default:
                    throw new Exception("Unknown mapping method: {$mapping_method}");
            }

        } catch (Exception $e) {
            tcs_log("Error in preview_multi_table_merge: " . $e->getMessage(), self::$log_target);
            throw $e;
        }
    }

    /**
     * Merge tables using like-for-like column matching
     */
    private static function merge_like_for_like(array $table_data, int $limit): array {
        $merged_data = [];
        $common_columns = null;

        // Find common columns across all tables
        foreach ($table_data as $table_name => $rows) {
            if (empty($rows)) continue;

            $table_columns = array_keys($rows[0]);
            if ($common_columns === null) {
                $common_columns = $table_columns;
            } else {
                $common_columns = array_intersect($common_columns, $table_columns);
            }
        }

        // Collect all rows from all tables with source info
        $all_rows = [];
        foreach ($table_data as $table_name => $rows) {
            foreach ($rows as $row) {
                $row['_source_table'] = $table_name;
                $row['_sort_id'] = $row['id'] ?? 0; // Use ID for sorting, default to 0 if no ID
                $all_rows[] = $row;
            }
        }

        // Sort by ID to mix tables
        usort($all_rows, function($a, $b) {
            return $a['_sort_id'] <=> $b['_sort_id'];
        });

        // Take only the limit and format the output
        $all_rows = array_slice($all_rows, 0, $limit);

        if (empty($common_columns)) {
            // No common columns, return all columns with table prefixes
            foreach ($all_rows as $row) {
                $table_name = $row['_source_table'];
                $merged_row = ['_source_table' => $table_name];

                foreach ($row as $column => $value) {
                    if ($column !== '_source_table' && $column !== '_sort_id') {
                        $merged_row[$table_name . '.' . $column] = $value;
                    }
                }
                $merged_data[] = $merged_row;
            }
        } else {
            // Merge rows with common columns
            foreach ($all_rows as $row) {
                $merged_row = ['_source_table' => $row['_source_table']];
                foreach ($common_columns as $column) {
                    $merged_row[$column] = $row[$column] ?? '';
                }
                $merged_data[] = $merged_row;
            }
        }

        return $merged_data;
    }

    /**
     * Merge tables using manual column mappings
     */
    private static function merge_manual(array $table_data, array $column_mappings, int $limit): array {
        $merged_data = [];

        if (empty($column_mappings)) {
            // No mappings defined, fall back to like-for-like
            return self::merge_like_for_like($table_data, $limit);
        }

        // Collect all rows from all tables with source info
        $all_rows = [];
        foreach ($table_data as $table_name => $rows) {
            foreach ($rows as $row) {
                $row['_source_table'] = $table_name;
                $row['_sort_id'] = $row['id'] ?? 0; // Use ID for sorting, default to 0 if no ID
                $all_rows[] = $row;
            }
        }

        // Sort by ID to mix tables
        usort($all_rows, function($a, $b) {
            return $a['_sort_id'] <=> $b['_sort_id'];
        });

        // Take only the limit and apply mappings
        $all_rows = array_slice($all_rows, 0, $limit);

        foreach ($all_rows as $row) {
            $table_name = $row['_source_table'];
            $merged_row = ['_source_table' => $table_name];

            // Apply each column mapping
            foreach ($column_mappings as $mapping) {
                $output_column = $mapping['output_column'] ?? '';
                $source_columns = $mapping['source_columns'] ?? [];
                $merge_strategy = $mapping['merge_strategy'] ?? 'first_non_empty';

                if (empty($output_column) || empty($source_columns)) {
                    continue;
                }

                $values = [];
                foreach ($source_columns as $source_column) {
                    if (strpos($source_column, '.') !== false) {
                        list($source_table, $column_name) = explode('.', $source_column, 2);
                        if ($source_table === $table_name && isset($row[$column_name])) {
                            $values[] = $row[$column_name];
                        }
                    }
                }

                // Apply merge strategy
                $merged_value = self::apply_merge_strategy($values, $merge_strategy, $mapping['separator'] ?? ', ');
                $merged_row[$output_column] = $merged_value;
            }

            $merged_data[] = $merged_row;
        }

        return $merged_data;
    }

    /**
     * Merge tables using unified field mapper
     */
    private static function merge_unified_field_mapper(array $table_data, array $unified_mappings, int $limit): array {
        $merged_data = [];
        $min_confidence = $unified_mappings['min_confidence'] ?? 75;
        $applied_mappings = $unified_mappings['applied'] ?? [];

        // Collect all rows from all tables with source info
        $all_rows = [];
        foreach ($table_data as $table_name => $rows) {
            foreach ($rows as $row) {
                $row['_source_table'] = $table_name;
                $row['_sort_id'] = $row['id'] ?? 0; // Use ID for sorting, default to 0 if no ID
                $all_rows[] = $row;
            }
        }

        // Sort by ID to mix tables
        usort($all_rows, function($a, $b) {
            return $a['_sort_id'] <=> $b['_sort_id'];
        });

        // Take only the limit and apply unified field mapper
        $all_rows = array_slice($all_rows, 0, $limit);

        foreach ($all_rows as $row) {
            $table_name = $row['_source_table'];

            // Remove our internal fields before processing
            unset($row['_source_table'], $row['_sort_id']);

            // Use unified field mapper to normalize the row automatically
            $normalized_row = \system\unified_field_mapper::normalize_entry($row, $table_name);

            // Add source table information
            $normalized_row['_source_table'] = $table_name;
            $normalized_row['_mapping_method'] = 'unified_field_mapper';
            $normalized_row['_min_confidence'] = $min_confidence;

            $merged_data[] = $normalized_row;
        }

        return $merged_data;
    }

    /**
     * Apply merge strategy to combine multiple values
     */
    private static function apply_merge_strategy(array $values, string $strategy, string $separator = ', '): string {
        $values = array_filter($values, function($v) { return $v !== null && $v !== ''; });

        if (empty($values)) {
            return '';
        }

        switch ($strategy) {
            case 'first_non_empty':
                return (string)$values[0];

            case 'concatenate':
                return implode($separator, $values);

            case 'sum':
                return (string)array_sum(array_map('floatval', $values));

            case 'average':
                return (string)(array_sum(array_map('floatval', $values)) / count($values));

            case 'max':
                return (string)max(array_map('floatval', $values));

            case 'min':
                return (string)min(array_map('floatval', $values));

            case 'count':
                return (string)count($values);

            default:
                return (string)$values[0];
        }
    }

    /**
     * Get tables matching wildcard patterns (fallback method)
     *
     * @param array $patterns Array of wildcard patterns
     * @return array Array of table information
     */
    public static function get_tables_by_patterns(array $patterns): array {
        $matching_tables = [];

        tcs_log("get_tables_by_patterns called with patterns: " . json_encode($patterns), self::$log_target);

        foreach ($patterns as $pattern) {
            try {
                // Convert wildcard pattern to SQL LIKE pattern
                $like_pattern = str_replace('*', '%', $pattern);
                tcs_log("Searching for tables with pattern: $pattern -> LIKE '$like_pattern'", self::$log_target);

                $query = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME LIKE ?";
                $stmt = database::rawQuery($query, [$like_pattern]);
                $table_names = $stmt->fetchAll(PDO::FETCH_COLUMN);

                tcs_log("Found " . count($table_names) . " tables for pattern $pattern: " . json_encode($table_names), self::$log_target);

                foreach ($table_names as $table_name) {
                    // Skip if already added
                    $already_added = false;
                    foreach ($matching_tables as $existing_table) {
                        if ($existing_table['name'] === $table_name) {
                            $already_added = true;
                            break;
                        }
                    }

                    if (!$already_added) {
                        // Try to get full table info, fallback to basic info if it fails
                        $table_info = self::get_table_info($table_name);
                        if ($table_info) {
                            $matching_tables[] = $table_info;
                        } else {
                            // Fallback to basic info
                            $matching_tables[] = [
                                'name' => $table_name,
                                'display_name' => self::format_table_display_name($table_name),
                                'category' => 'data_table',
                                'row_count' => 0,
                                'columns' => [],
                                'description' => 'Table (info unavailable)'
                            ];
                        }
                    }
                }
            } catch (Exception $e) {
                tcs_log("Error getting tables for pattern $pattern: " . $e->getMessage(), self::$log_target);
                continue;
            }
        }

        return $matching_tables;
    }
}
